#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: _setting.py
Description: 常用配置文件
Author: Peng
Email: la<PERSON><PERSON><PERSON><PERSON>@gmail.com
Date: 2023-10-13
"""
import json
import argparse
from pathlib import Path
from public_utils_configs.util.initialize_phone_util import InitializePhone
initialize_phone_obj = InitializePhone()

# 项目目录
DIR_PROJECT = Path(__file__).resolve().parent.parent  # 项目根目录
DIR_CONF = Path(__file__).resolve().parent  # 配置文件目录
# 命令行参数解析
parser = argparse.ArgumentParser()
parser.add_argument('-c', '--conf', default=None, help='conf file path')
parser.add_argument('reader', nargs='?', default=None, help='reader yaml path, like example.yml')
parser.add_argument('-i', '--inner', default=None, help='mysql conn network, use case 1|0')
# 参数字典
ARGS = parser.parse_args()

COUNTRY_NAME_MAP_PATH = f'{DIR_CONF}/country_name_map.json'
with open(COUNTRY_NAME_MAP_PATH, 'r') as f:
    COUNTRY_NAME_MAP = json.load(f)
