<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"><head id="j_idt2">
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta lang="zh" http-equiv="Content-Type" content="text/html;charset=utf-8" charset="utf-8" />

	
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0" />
	
	<meta name="apple-mobile-web-app-capable" content="yes" />
	
	<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
		<meta name="Description" content="This patent search tool allows you not only to search the PCT database of about 2 million International Applications but also the worldwide patent collections. This search facility features: flexible search syntax; automatic word stemming and relevance ranking; as well as graphical results." />
		<meta name="Cache-Control" content="no-cache,no-store,must-revalidate" />
		<meta name="Expires" content="0" />
		<meta name="Pragma" content="no-cache" />
	
	<title>WIPO – 检索国际和国家专利汇编</title><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/theme.css.jsf?ln=primefaces-wipo" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/primefaces/fa/font-awesome.css.xhtml" /><script type="text/javascript" src="/search/javax.faces.resource/omnifaces.js.jsf?ln=omnifaces&amp;v=2.6.9"></script><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/components.css.jsf?ln=w-ps-cc" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/components.css.jsf?ln=ps-cc" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/common/common.css.jsf" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/common/primefaces-custom.css.jsf" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/common/wfaces-custom.css.jsf" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/wipo/ps-ulf-compatibility.css.jsf" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/result-list.css.jsf" /><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/primefaces/components.css.xhtml" /><script type="text/javascript" src="/search/javax.faces.resource/primefaces/jquery/jquery.js.xhtml"></script><script type="text/javascript" src="/search/javax.faces.resource/primefaces/core.js.xhtml"></script><script type="text/javascript" src="/search/javax.faces.resource/primefaces/components.js.xhtml"></script><script type="text/javascript" src="/search/javax.faces.resource/primefaces/jquery/jquery-plugins.js.xhtml"></script><script type="text/javascript" src="/search/javax.faces.resource/jsf.js.jsf?ln=javax.faces"></script><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/charts/charts.css.jsf?ln=primefaces&amp;v=6.1.4" /><script type="text/javascript" src="/search/javax.faces.resource/charts/charts.js.jsf?ln=primefaces&amp;v=6.1.4"></script><script type="text/javascript" src="/search/javax.faces.resource/hotkey/hotkey.js.jsf?ln=primefaces&amp;v=6.1.4"></script><style type="text/css">
		/*	When the drawerTriggering element has a top/bottom padding/margin (as the mainmenu items), 
			the position of the drawer must be updated correspondignly.*/
		.settings-drawer.b-drawer.wf-drawer-fix-position {
			top: -0.75rem;
		}	
	</style><style type="text/css">
		/*	When the drawerTriggering element has a top/bottom padding/margin (as the mainmenu items), 
			the position of the drawer must be updated correspondignly.*/
		.feedback-drawer.b-drawer.wf-drawer-fix-position {
			top: -0.75rem;
		}
		
		.feedback-message {
			margin-top: 1rem;
		}
	</style><style type="text/css">
		/*	When the drawerTriggering element has a top/bottom padding/margin (as the mainmenu items), 
			the position of the drawer must be updated correspondignly.*/
		.feedback-drawer.b-drawer.wf-drawer-fix-position {
			top: -0.75rem;
		}
	</style><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/autocomplete.css.jsf?ln=autocomplete" /><style type="text/css">
		.queryTree-drawer .queryTree-outputpanel {
			border: 1px solid #D3D3D3;
			padding: 1rem 0;
			height:300px;
			font-size: 0.875em !important;
			overflow: auto;
		}
	</style><style type="text/css">
		.hotkeys-container {
			display: flex;
			flex-wrap: wrap;
		}
		
		.ps-panel {
			margin-top: 0;
			outline: none;
		}
		
		.hotkeys-list {
			color: gray;
		}
		
		.hotkeys-list tr td:first-child {
			width: 50%;
		}
		
		.hotkeys-list tr td:last-child {
			width: 50%;
			white-space: nowrap;
		}
		
		.hotkeys-list .hotkeyLabel {
			white-space: nowrap;
		}
		
		.hotkeys-list .hotkeyCombination {
			display: flex;
			align-items: center;
			margin: 0.25rem; 
		}
		
		.hotkeys-list kbd {
		    display: inline-block;
		    padding: 0.375rem 0.5rem;
		    margin: 0 0.25rem;
		    background-color: #f0f0f0;
		    border: 1px solid #ccc;
		    min-width: 2rem;
		    text-align: center;
		}
	
		.hotkeys-list kbd .icon-wrapper {
	 		opacity: 1;
	 		margin: 0.25rem;
		}
	</style><link type="text/css" rel="stylesheet" href="/search/javax.faces.resource/css/analysis.css.jsf" /><script type="text/javascript">
		function barChartExtender() {
			// this = chart widget instance
			// this.cfg = options
			
			this.cfg.grid = {
				shadow: false,
				background: 'transparent',
				borderWidth: 0,
				gridLineWidth: 0.5,
				gridLineColor: '#ccc',
				shadowWidth: 0
			};
			
			this.cfg.axesDefaults = {
		        tickRenderer: $.jqplot.CanvasAxisTickRenderer,
		        tickOptions: {
					fontSize: '0.875rem'
		        }
		    };
			
			this.cfg.cursor = {
				showTooltip: false,
				clickReset: false,
				dblClickReset: false
			};
			
			this.cfg.highlighter.tooltipAxes = 'y';
		}
		
		function pieChartExtender() {
			this.cfg.grid = {
				shadow: false,
				background: 'transparent',
				borderWidth: 0,
				gridLineColor: '#ccc'
			};
			
			this.cfg.gridPadding = {
				top: 0, bottom: 0, left: 0, right: 0
			};
		}
	</script><script type="text/javascript">
		function barChartExtender() {
			// this = chart widget instance
			// this.cfg = options
			
			this.cfg.grid = {
				shadow: false,
				background: 'transparent',
				borderWidth: 0,
				gridLineWidth: 0.5,
				gridLineColor: '#ccc',
				shadowWidth: 0
			};
			
			this.cfg.axesDefaults = {
		        tickRenderer: $.jqplot.CanvasAxisTickRenderer,
		        tickOptions: {
					fontSize: '0.875rem'
		        }
		    };
			
			this.cfg.cursor = {
				showTooltip: false,
				clickReset: false,
				dblClickReset: false
			};
			
			this.cfg.highlighter.tooltipAxes = 'y';
		}
		
		function lineChartExtender() {
			this.cfg.grid = {
				shadow: false,
				background: 'transparent',
				borderWidth: 0,
				gridLineColor: '#ccc'
			};
			
			this.cfg.gridPadding = {
				top: 0, bottom: 0, left: 0, right: 0
			};
		}
	</script><script type="text/javascript">if(window.PrimeFaces){PrimeFaces.settings.locale='zh';}</script>
		<link href="/search/javax.faces.resource/w/css/wfaces.css.xhtml?v=1.0&amp;b=" rel="stylesheet" />




	<script>
		var APP_CTX='/search';
	</script>
		<script src="/search/javax.faces.resource/w/js/wfaces.js.xhtml?v=1.0&amp;b="></script><script type="text/javascript">
		epctmsg.lbl_designations_specific="特定";
		epctmsg.lbl_designations_none="无";
		epctmsg.lbl_designations_all="所有";
		epctmsg.lbl_designations_changeSelec="更改选择";
		epctmsg.lbl_designations_chooseDesig="选择个别指定国";
		epctmsg.no_results_text="以下内容无匹配项：";
	</script><script type="text/javascript" src="/search/javax.faces.resource/js/psa.js.jsf"></script>
			<script src="https://webcomponents.wipo.int/polyfills/webcomponents-loader.js"></script>
	  		
	  		<script src="https://webcomponents.wipo.int/wipo-navbar/wipo-navbar.js"></script><script type="text/javascript" src="/search/javax.faces.resource/js/components.js.jsf?ln=w-ps-cc"></script>
		<script id="taptaWidgetUrl" src="https://patentscope.wipo.int/translate/widget/app.js" data-mt-widget-site="patentscope"></script>
		<script>
			var taptaUrl= false;
		</script><script type="text/javascript" src="/search/javax.faces.resource/js/result.js.jsf"></script>
	
	<script src="/search/resources/js/lib/require.js" type="text/javascript" charset="utf-8"></script><script type="text/javascript" src="/search/javax.faces.resource/js/autocomplete.js.jsf?ln=autocomplete"></script></head><body dir="ltr">
<div class="wf-overlayPanel"></div><div id="pageBlockUI" class="ui-blockui-content ui-widget ui-widget-content ui-corner-all ui-helper-hidden ui-shadow">
	<div class="b-infobox b-infobox--has-spinner">
	  <h4 class="b-infobox__title">
	    正在处理
	  </h4>
	  <div class="b-infobox__text">
	    <p>
	    	请稍候...
	    </p>
	  </div>
	</div></div><script id="pageBlockUI_s" type="text/javascript">$(function(){PrimeFaces.cw("BlockUI","widget_pageBlockUI",{id:"pageBlockUI",block:"@(.b-page)"});});</script>
<div class="b-page b-page--application">
	<div class="b-navigation b-navigation--has-close"><wipo-navbar appId='app-0072' applicationName='PATENTSCOPE' applicationLink='/search/zh' applicationCategory='patents' login='/search/wiposso/login' logout='/search/wiposso/logout' sso='unauthenticated' language='zh' languageOptions='[{"code":"en","name":null,"link":"/search/en/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"fr","name":null,"link":"/search/fr/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"de","name":null,"link":"/search/de/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"es","name":null,"link":"/search/es/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"pt","name":null,"link":"/search/pt/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"ru","name":null,"link":"/search/ru/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"ja","name":null,"link":"/search/ja/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"zh","name":null,"link":null,"targetTab":null},{"code":"ko","name":null,"link":"/search/ko/result.jsf?_vid=P20-MDFS25-41499","targetTab":null},{"code":"ar","name":null,"link":"/search/ar/result.jsf?_vid=P20-MDFS25-41499","targetTab":null}]' userOptions='[{"code":null,"name":"会话查询","link":"/search/zh/reg/user_session_queries.jsf","targetTab":"_self"},{"code":null,"name":"保存的查询","link":"/search/zh/reg/user_queries.jsf","targetTab":"_self"},{"code":null,"name":"MARKUSH批","link":"/search/zh/chemc/batches.jsf","targetTab":"_self"},{"code":null,"name":"已关注申请","link":"/search/zh/reg/watching.jsf","targetTab":"_self"}]' helpOptions='[{"code":"contact","name":null,"link":"https://www3.wipo.int/contact/zh/area.jsp?area=patentscope-db","targetTab":null},{"code":"faq","name":null,"link":"https://www.wipo.int/patentscope/zh/faqs_patentscope.html","targetTab":null}]' helpMore='[{"code":null,"name":"PATENTSCOPE用户社区","link":"https://www.linkedin.com/groups/9811620/","targetTab":"_blank"},{"code":null,"name":"PATENTSCOPE帮助","link":"/search/zh/help/help.jsf","targetTab":"_self"},{"code":null,"name":"使用条款","link":"https://www.wipo.int/patentscope/zh/data/terms_patentscope.html","targetTab":"_blank"},{"code":null,"name":"隐私政策","link":"https://www.wipo.int/tools/zh/privacy_policy-ipportal.html","targetTab":"_blank"}]' hide-search='true'><div style='background:black; height: 48px; width: 100%'></div></wipo-navbar>

	<div class="ps-mainmenu--container">
<form id="formMainMenu" name="formMainMenu" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="formMainMenu" value="formMainMenu" />

	
	
			<div class="ps-mainmenu">

				<div class="ps-mainmenu--item">
				</div>
				
				<div class="ps-mainmenu--item">
	
	<div class="ps-drawer-trigger b-pointer b-pointer--is-toggle" aria-controls="feedback_drawer"><a id="formMainMenu:feedbackLink" href="#" onclick="mojarra.ab(this,event,'action',0,'feedbackForm');return false">反馈</a>
	</div>
				</div>
			
				<div class="ps-mainmenu--item">
		<button id="formMainMenu:triggerSearchMenu" class="ps-plain-button " type="button">检索
			<span class="icon-wrapper small-arrow-down-icon"></span>
		</button><div id="formMainMenu:j_idt110" class="ui-menu ui-menu-dynamic ui-widget ui-widget-content ui-corner-all ui-helper-clearfix ui-shadow ps-mainmenu--submenu" role="menu"><div tabindex="0" class="ui-helper-hidden-accessible"></div><ul class="ui-menu-list ui-helper-reset"><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:simpleSearch" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/search.jsf';PrimeFaces.ab({s:&quot;formMainMenu:simpleSearch&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">简单</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:advancedSearch" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/advancedSearch.jsf';PrimeFaces.ab({s:&quot;formMainMenu:advancedSearch&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">高级检索</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:structuredSearch" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/structuredSearch.jsf';PrimeFaces.ab({s:&quot;formMainMenu:structuredSearch&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">字段组合</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:clir" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/clir/clir.jsf';PrimeFaces.ab({s:&quot;formMainMenu:clir&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">跨语种扩展</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/chemc/chemc.jsf';PrimeFaces.ab({s:&quot;formMainMenu:j_idt111&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">化合物 (需要登录)</span></a></li></ul></div><script id="formMainMenu:j_idt110_s" type="text/javascript">$(function(){PrimeFaces.cw("PlainMenu","widget_formMainMenu_j_idt110",{id:"formMainMenu:j_idt110",overlay:true,my:"left top",at:"left bottom",trigger:"formMainMenu:triggerSearchMenu",triggerEvent:"click"});});</script>
				</div>

	
				<div class="ps-mainmenu--item">
		<button id="formMainMenu:triggerBrowseMenu" class="ps-plain-button " type="button">浏览
			<span class="icon-wrapper small-arrow-down-icon"></span>
		</button><div id="formMainMenu:j_idt121" class="ui-menu ui-menu-dynamic ui-widget ui-widget-content ui-corner-all ui-helper-clearfix ui-shadow ui-menu-toggleable ps-mainmenu--submenu" role="menu"><div tabindex="0" class="ui-helper-hidden-accessible"></div><ul class="ui-menu-list ui-helper-reset"><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:browseByWeek" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/resultWeeklyBrowse.jsf';PrimeFaces.ab({s:&quot;formMainMenu:browseByWeek&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">按星期（专利合作条约）浏览</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:pctBrowse" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/pctBrowse.jsf';PrimeFaces.ab({s:&quot;formMainMenu:pctBrowse&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">公报档案</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:sequenceListing" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/sequences.jsf';PrimeFaces.ab({s:&quot;formMainMenu:sequenceListing&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">序列表</span></a></li><li id="formMainMenu:j_idt122" class="ui-widget-header ui-corner-all"><h3><span class="ui-icon ui-icon-triangle-1-s"></span>进入国家阶段</h3></li><li class="ui-menuitem ui-widget ui-corner-all ui-submenu-child" role="menuitem"><a tabindex="-1" id="formMainMenu:pctNPE" class="ui-menuitem-link ui-corner-all" href="/search/static/npe/npe.zip"><span class="ui-menuitem-text"><table>
<tbody>
<tr>
<td>进入国家阶段 完整下载</td>
<td><label class="icon-wrapper http-download-icon ps-left-space"></label></td>
<td>
									<a href="ftp://ftp.wipo.int/patentscope/pdf/npe/npe.zip" class="icon-wrapper ftp-download-icon ps-left-space"></a></td>
</tr>
</tbody>
</table>
</span></a></li><li class="ui-menuitem ui-widget ui-corner-all ui-submenu-child" role="menuitem"><a tabindex="-1" id="formMainMenu:pctNPEinc" class="ui-menuitem-link ui-corner-all" href="/search/static/npe/npe-incremental.zip"><span class="ui-menuitem-text"><table>
<tbody>
<tr>
<td>进入国家阶段 增量下载（最近7天）</td>
<td><label class="icon-wrapper http-download-icon ps-left-space"></label></td>
<td>
									<a href="ftp://ftp.wipo.int/patentscope/pdf/npe/npe-incremental.zip" class="icon-wrapper ftp-download-icon ps-left-space"></a></td>
</tr>
</tbody>
</table>
</span></a></li><li id="formMainMenu:j_idt131" class="ui-widget-header ui-corner-all"><h3><span class="ui-icon ui-icon-triangle-1-s"></span>规范文件</h3></li><li class="ui-menuitem ui-widget ui-corner-all ui-submenu-child" role="menuitem"><a tabindex="-1" id="formMainMenu:authorityFileST37" class="ui-menuitem-link ui-corner-all" href="/search/static/authority/WO_AF.zip"><span class="ui-menuitem-text"><table>
<tbody>
<tr>
<td>规范文件 下载标准ST37</td>
<td><label class="icon-wrapper http-download-icon ps-left-space"></label></td>
<td>
									<a href="ftp://ftp.wipo.int/patentscope/pdf/gazette/WO_AF.zip" class="icon-wrapper ftp-download-icon ps-left-space"></a></td>
</tr>
</tbody>
</table>
</span></a></li></ul></div><script id="formMainMenu:j_idt121_s" type="text/javascript">$(function(){PrimeFaces.cw("PlainMenu","widget_formMainMenu_j_idt121",{id:"formMainMenu:j_idt121",toggleable:true,overlay:true,my:"left top",at:"left bottom",trigger:"formMainMenu:triggerBrowseMenu",triggerEvent:"click"});});</script>
		
				</div><div id="formMainMenu:toolsMainMenu" class="ps-mainmenu--item js-multiDocumentDownloadEnabled">
		<button id="formMainMenu:triggerToolsMenu" class="ps-plain-button " type="button">工具
			<span class="icon-wrapper small-arrow-down-icon"></span>
		</button><div id="formMainMenu:j_idt145" class="ui-menu ui-menu-dynamic ui-widget ui-widget-content ui-corner-all ui-helper-clearfix ui-shadow ps-mainmenu--submenu" role="menu"><div tabindex="0" class="ui-helper-hidden-accessible"></div><ul class="ui-menu-list ui-helper-reset"><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="https://patentscope.wipo.int/translate/translate.jsf?interfaceLanguage=zh" target="_blank"><span class="ui-menuitem-text">WIPO Translate</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="https://wipopearl.wipo.int/zh/linguistic" target="_blank"><span class="ui-menuitem-text">WIPO Pearl</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:greenInventory" class="ui-menuitem-link ui-corner-all" href="https://www.wipo.int/classifications/ipc/green-inventory/home" target="_blank"><span class="ui-menuitem-text">国际专利分类绿色清单</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:aiIndexEn" class="ui-menuitem-link ui-corner-all" href="https://www.wipo.int/tech_trends/en/artificial_intelligence/patentscope.html" target="_blank"><span class="ui-menuitem-text">人工智能指数</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:covid19" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/covid19.jsf';PrimeFaces.ab({s:&quot;formMainMenu:covid19&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">支持应对新冠肺炎的工作</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" id="formMainMenu:sdg" class="ui-menuitem-link ui-corner-all" href="#" onclick="document.location.href='/search/zh/sdg.jsf';PrimeFaces.ab({s:&quot;formMainMenu:sdg&quot;,f:&quot;formMainMenu&quot;});return false;"><span class="ui-menuitem-text">可持续发展目标（SDG）</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="https://www.wipo.int/patent_register_portal" target="_blank"><span class="ui-menuitem-text">专利登记簿门户</span></a></li></ul></div><script id="formMainMenu:j_idt145_s" type="text/javascript">$(function(){PrimeFaces.cw("PlainMenu","widget_formMainMenu_j_idt145",{id:"formMainMenu:j_idt145",overlay:true,my:"left top",at:"left bottom",trigger:"formMainMenu:triggerToolsMenu",triggerEvent:"click"});});</script></div>
	
	
				<div class="ps-mainmenu--item">
	
	<div class="ps-drawer-trigger b-pointer b-pointer--is-toggle" aria-controls="settings_drawer">
						<span>设置</span>
	</div>
				</div>
				
		    </div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
	</div>
		<div id="settings_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      settings-drawer">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">设置</h2>
					<div class="b-drawer__title-actions"><button id="j_idt164" name="j_idt164" class="b-button" onclick="PrimeFaces.ab({s:&quot;j_idt164&quot;,f:&quot;settingsForm&quot;,u:&quot;settingsForm&quot;});return false;" type="submit"><span class="ui-button-text ui-c">重置</span></button><script id="j_idt164_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt164",{id:"j_idt164"});</script><button id="j_idt165" name="j_idt165" class="b-button b-button--is-type_primary js-close-pointer js-close-button" onclick="onDrawerClose(this);" type="button" aria-controls="settings_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt165_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt165",{id:"j_idt165"});</script>
					</div>
			</div>

			<div class="ps-drawer--content">
<form id="settingsForm" name="settingsForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="settingsForm" value="settingsForm" />
<div id="settingsForm:j_idt167" class="ui-tabmenu ui-widget ui-widget-content ui-corner-all ps-tabmenu ps-tabmenu--is-plain"><ul class="ui-tabmenu-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all" role="tablist"><li class="ui-tabmenuitem ui-state-default ui-state-active ui-corner-top" role="tab" aria-expanded="true" aria-selected="true"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.ab({s:&quot;settingsForm:j_idt168&quot;,u:&quot;settingsForm&quot;,f:&quot;settingsForm&quot;});return false;"><span class="ui-menuitem-text">查询</span></a></li><li class="ui-tabmenuitem ui-state-default ui-corner-top" role="tab" aria-expanded="false" aria-selected="false"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.ab({s:&quot;settingsForm:j_idt169&quot;,u:&quot;settingsForm&quot;,f:&quot;settingsForm&quot;});return false;"><span class="ui-menuitem-text">专利局</span></a></li><li class="ui-tabmenuitem ui-state-default ui-corner-top" role="tab" aria-expanded="false" aria-selected="false"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.ab({s:&quot;settingsForm:j_idt170&quot;,u:&quot;settingsForm&quot;,f:&quot;settingsForm&quot;});return false;"><span class="ui-menuitem-text">结果</span></a></li><li class="ui-tabmenuitem ui-state-default ui-corner-top" role="tab" aria-expanded="false" aria-selected="false"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.ab({s:&quot;settingsForm:j_idt171&quot;,u:&quot;settingsForm&quot;,f:&quot;settingsForm&quot;});return false;"><span class="ui-menuitem-text">下载</span></a></li><li class="ui-tabmenuitem ui-state-default ui-corner-top" role="tab" aria-expanded="false" aria-selected="false"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.ab({s:&quot;settingsForm:j_idt172&quot;,u:&quot;settingsForm&quot;,f:&quot;settingsForm&quot;});return false;"><span class="ui-menuitem-text">界面</span></a></li></ul></div><script id="settingsForm:j_idt167_s" type="text/javascript">PrimeFaces.cw("TabMenu","widget_settingsForm_j_idt167",{id:"settingsForm:j_idt167"});</script><div id="settingsForm:querySection" class="ui-outputpanel ui-widget">
	<div id="settingsForm:j_idt174" class="b-edit-panel">
		<div class="b-edit-panel__content">

	<div id="settingsForm:j_idt175" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section b-view-panel__section--slots_two">
    
    <div id="settingsForm:j_idt176" class="b-view-panel__slot w-slot">
	<div id="settingsForm:queryLanguage">
		<div class="b-input b-input-dropdown b-input--has-floatlabel b-input--is-select                         ">
	
			<div class="b-input__table">
				<div class="b-input__table-td b-input__table-td__dropdown">
					<label class="b-input__label b-input__label"><label>查询语言</label>
					</label><select id="settingsForm:queryLanguage:input" name="settingsForm:queryLanguage:input" class="b-input__input b-input__dropdown-input" size="1" onselect="" tabindex="" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;settingsForm:queryLanguage:input&quot;,u:&quot;@(.js-searchTextLanguage-option)&quot;});">	<option value="zh">中文</option>
	<option value="da">丹麦语</option>
	<option value="ru">俄语</option>
	<option value="bg">保加利亚语</option>
	<option value="hr">克罗地亚文</option>
	<option value="hu">匈牙利语</option>
	<option value="id">印度尼西亚语</option>
	<option value="kk">哈萨克文</option>
	<option value="tr">土耳其文</option>
	<option value="sr">塞尔维亚文</option>
	<option value="he">希伯来语</option>
	<option value="el">希腊语</option>
	<option value="de">德语</option>
	<option value="it">意大利语</option>
	<option value="lv">拉脱维亚语</option>
	<option value="no">挪威语</option>
	<option value="cs">捷克文</option>
	<option value="sk">斯洛伐克文</option>
	<option value="ja">日语</option>
	<option value="ge">格鲁吉亚语</option>
	<option value="fr">法语</option>
	<option value="pl">波兰语</option>
	<option value="th">泰语</option>
	<option value="et">爱沙尼亚语</option>
	<option value="sv">瑞典语</option>
	<option value="lt">立陶宛语 </option>
	<option value="ro">罗马尼亚语</option>
	<option value="lo">老挝语</option>
	<option value="fi">芬兰文</option>
	<option value="en">英语</option>
	<option value="nl">荷兰语</option>
	<option value="pt">葡萄牙语</option>
	<option value="es">西班牙语</option>
	<option value="vn">越南语</option>
	<option value="ar">阿拉伯语</option>
	<option value="ko">韩语</option>
	<option value="ms">马来语</option>
	<option value="kh">高棉语</option>
	<option value="GUI" selected="selected">默认</option>
</select>
	
				</div>
			</div><div id="settingsForm:queryLanguage:error"></div><div id="settingsForm:queryLanguage:info"></div>
		</div>
	</div>
	</div>
			
		</div>
	</div>

	<div id="settingsForm:j_idt237" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section b-view-panel__section--slots_three">
    
    <div id="settingsForm:j_idt238" class="b-view-panel__slot w-slot">
	<div id="settingsForm:stemmingOption">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="settingsForm:stemmingOption:input" type="checkbox" name="settingsForm:stemmingOption:input" checked="checked" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;settingsForm:stemmingOption:input&quot;,u:&quot;@(.js-stemming-option)&quot;});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">词根提取
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="settingsForm:stemmingOption:error"></div><div id="settingsForm:stemmingOption:info"></div>
	</div>
	</div>
	</div>
    
    <div id="settingsForm:j_idt258" class="b-view-panel__slot w-slot">
	<div id="settingsForm:singleFamilyMemberOption">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="settingsForm:singleFamilyMemberOption:input" type="checkbox" name="settingsForm:singleFamilyMemberOption:input" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;settingsForm:singleFamilyMemberOption:input&quot;,u:&quot;@(.js-singleFamilyMember-option)&quot;});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">单一族成员
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="settingsForm:singleFamilyMemberOption:error"></div><div id="settingsForm:singleFamilyMemberOption:info"></div>
	</div>
	</div>
	</div>
    
    <div id="settingsForm:j_idt278" class="b-view-panel__slot w-slot">
	<div id="settingsForm:includeNplOption">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="settingsForm:includeNplOption:input" type="checkbox" name="settingsForm:includeNplOption:input" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;settingsForm:includeNplOption:input&quot;,u:&quot;@(.js-includeNPL-option)&quot;});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">包括NPL
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="settingsForm:includeNplOption:error"></div><div id="settingsForm:includeNplOption:info"></div>
	</div>
	</div>
	</div>
			
		</div>
	</div>

	<div id="settingsForm:j_idt315" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section b-view-panel__section--slots_three">
    
    <div id="settingsForm:j_idt316" class="b-view-panel__slot w-slot">
	<div id="settingsForm:sortOption">
		<div class="b-input b-input-dropdown b-input--has-floatlabel b-input--is-select                         ">
	
			<div class="b-input__table">
				<div class="b-input__table-td b-input__table-td__dropdown">
					<label class="b-input__label b-input__label">排序依据：
					</label><select id="settingsForm:sortOption:input" name="settingsForm:sortOption:input" class="b-input__input b-input__dropdown-input" size="1" onselect="" tabindex="" onchange="mojarra.ab(this,event,'change',0,0)">	<option value="-score" selected="selected">相关性</option>
	<option value="-DP">公布日降序</option>
	<option value="+DP">公布日升序</option>
	<option value="-AD">申请日降序</option>
	<option value="+AD">申请日升序</option>
</select>
	
				</div>
			</div><div id="settingsForm:sortOption:error"></div><div id="settingsForm:sortOption:info"></div>
		</div>
	</div>
	</div>
    
    <div id="settingsForm:j_idt338" class="b-view-panel__slot w-slot">
	<div id="settingsForm:lengthOption">
		<div class="b-input b-input-dropdown b-input--has-floatlabel b-input--is-select                         ">
	
			<div class="b-input__table">
				<div class="b-input__table-td b-input__table-td__dropdown">
					<label class="b-input__label b-input__label"><label>表长</label>
					</label><select id="settingsForm:lengthOption:input" name="settingsForm:lengthOption:input" class="b-input__input b-input__dropdown-input" size="1" onselect="" tabindex="" onchange="mojarra.ab(this,event,'change',0,0)">	<option value="10" selected="selected">10</option>
	<option value="50">50</option>
	<option value="100">100</option>
	<option value="200">200</option>
</select>
	
				</div>
			</div><div id="settingsForm:lengthOption:error"></div><div id="settingsForm:lengthOption:info"></div>
		</div>
	</div>
	</div>
    
    <div id="settingsForm:j_idt363" class="b-view-panel__slot w-slot">
	<div id="settingsForm:j_idt364">
		<div class="b-input b-input-dropdown b-input--has-floatlabel b-input--is-select                         ">
	
			<div class="b-input__table">
				<div class="b-input__table-td b-input__table-td__dropdown">
					<label class="b-input__label b-input__label">结果列表视图
					</label><select id="settingsForm:j_idt364:input" name="settingsForm:j_idt364:input" class="b-input__input b-input__dropdown-input" size="1" onselect="" tabindex="" onchange="mojarra.ab(this,event,'change',0,0)">	<option value="SIMPLE_VIEW">简洁</option>
	<option value="DOUBLE_VIEW">双</option>
	<option value="ALL_VIEW" selected="selected">全文</option>
	<option value="ALL_VIEW_IMAGE">全文 + 图像</option>
	<option value="IMAGE_ONLY">图像</option>
	<option value="MULTI_LANGS">多列</option>
</select>
	
				</div>
			</div><div id="settingsForm:j_idt364:error"></div><div id="settingsForm:j_idt364:info"></div>
		</div>
	</div>
	</div>
			
		</div>
	</div>
		</div>
	</div></div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
			</div>
		</div>
		<div id="feedback_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      feedback-drawer">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">反馈</h2>
					<div class="b-drawer__title-actions"><button id="j_idt973" name="j_idt973" class="b-button js-close-pointer js-close-button" onclick="PrimeFaces.ab({s:&quot;j_idt973&quot;,p:&quot;j_idt973&quot;,u:&quot;feedbackForm&quot;});return false;" type="submit" aria-controls="feedback_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt973_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt973",{id:"j_idt973"});</script>
					</div>
			</div>

			<div class="ps-drawer--content">
<form id="feedbackForm" name="feedbackForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="feedbackForm" value="feedbackForm" />
<span id="feedbackForm:feedbackPanel">
			<div class="feedback-message"><p>请告诉我们您对PATENTSCOPE的看法，或者您认为还缺少什么或可以如何改进。</p>
			</div>
	<div id="feedbackForm:j_idt977" class="b-edit-panel">
		<div class="b-edit-panel__content">

	<div id="feedbackForm:j_idt978" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section ">
    
    <div id="feedbackForm:j_idt979" class="b-view-panel__slot w-slot">
	<div id="feedbackForm:feedback">
		<div class="b-input b-input-text-area b-input--has-floatlabel      b-input--is-required                      ">
	
			<div class="b-input__table">
				<div class="b-input__table-td">
	
					<label class="b-input__label"><span class="ie-bg-label">您的反馈</span></label><textarea id="feedbackForm:feedback:input" name="feedbackForm:feedback:input" cols="20" rows="3" tabindex="" maxlength="2147483647" style="overflow-y: auto; " aria-required="true" class="ui-inputfield ui-inputtextarea ui-widget ui-state-default ui-corner-all b-input__text-area-input  ui-inputtextarea-resizable"></textarea><script id="feedbackForm:feedback:input_s" type="text/javascript">$(function(){PrimeFaces.cw("InputTextarea","widget_feedbackForm_feedback_input",{id:"feedbackForm:feedback:input",autoResize:true,counter:"feedbackForm:feedback:display",counterTemplate:"剩余 {0} 个字符。"});});</script>
	
				</div>
			</div><div id="feedbackForm:feedback:error"></div><div id="feedbackForm:feedback:info"></div>
		</div>
	</div>
	<div id="feedbackForm:contactEmail">
		<div class="b-input b-input-text b-input--has-floatlabel       b-input--is-required                   ">
			<div class="b-input__table" style="width: 100%">
				<div class="b-input__table-td">
					<label class="b-input__label">联系电子邮箱
					</label><input id="feedbackForm:contactEmail:input" type="text" name="feedbackForm:contactEmail:input" autocomplete="off" class="b-input__input b-input__text-input                       " onkeydown="" onkeyup="" style="" tabindex="" title="" />
				</div><div id="feedbackForm:contactEmail:buttons" class="b-input__button-wrapper"><button id="feedbackForm:contactEmail:j_idt1004" name="feedbackForm:contactEmail:j_idt1004" class="b-button b-button--is-type_primary" onclick="PrimeFaces.ab({s:&quot;feedbackForm:contactEmail:j_idt1004&quot;,p:&quot;feedbackForm&quot;,u:&quot;feedbackForm&quot;,onco:function(xhr,status,args){if (args &amp;&amp; args.closeDrawer) { PSComponents.closeDrawer('feedback_drawer'); };}});return false;" type="submit"><span class="ui-button-text ui-c">发送</span></button><script id="feedbackForm:contactEmail:j_idt1004_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_feedbackForm_contactEmail_j_idt1004",{id:"feedbackForm:contactEmail:j_idt1004"});</script></div>
	
		     </div><div id="feedbackForm:contactEmail:error"></div><div id="feedbackForm:contactEmail:info"></div>
	    </div>
    </div>
	</div>
			
		</div>
	</div>
		</div>
	</div><span id="feedbackForm:j_idt1057"></span><script type="text/javascript">$(function(){PrimeFaces.focus('feedbackForm:feedback:input');});</script></span><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
			</div>
		</div>
		<div id="goto_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      goto-drawer">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">查看申请</h2>
			</div>

			<div class="ps-drawer--content">
<form id="gotoForm" name="gotoForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="gotoForm" value="gotoForm" />

	<div id="gotoForm:j_idt1086" class="b-edit-panel">
		<div class="b-edit-panel__content">

	<div id="gotoForm:j_idt1087" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section ">
    
    <div id="gotoForm:j_idt1088" class="b-view-panel__slot w-slot">
	<div id="gotoForm:gotoApplicationNumber">
		<div class="b-input b-input-text b-input--has-floatlabel       b-input--is-required                   ">
			<div class="b-input__table" style="width: 100%">
				<div class="b-input__table-td">
					<label class="b-input__label">申请ID/号
					</label><input id="gotoForm:gotoApplicationNumber:input" type="text" name="gotoForm:gotoApplicationNumber:input" autocomplete="off" class="b-input__input b-input__text-input                       " onkeydown="if (event.keyCode == 13) {$('.js-goto-button').click(); return false;}" onkeyup="" style="" tabindex="" title="" />
				</div><div id="gotoForm:gotoApplicationNumber:buttons" class="b-input__button-wrapper"><button id="gotoForm:gotoApplicationNumber:j_idt1090" name="gotoForm:gotoApplicationNumber:j_idt1090" class="b-button-medium primary js-goto-button" onclick="PrimeFaces.ab({s:&quot;gotoForm:gotoApplicationNumber:j_idt1090&quot;,u:&quot;gotoForm&quot;});return false;" type="submit"><span class="ui-button-icon-left ui-icon ui-c b-icon b-icon--search"></span><span class="ui-button-text ui-c"></span></button><script id="gotoForm:gotoApplicationNumber:j_idt1090_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_gotoForm_gotoApplicationNumber_j_idt1090",{id:"gotoForm:gotoApplicationNumber:j_idt1090"});</script></div>
	
		     </div><div id="gotoForm:gotoApplicationNumber:error"></div><div id="gotoForm:gotoApplicationNumber:info"></div>
	    </div>
    </div>
	</div>
			
		</div>
	</div>
		</div>
	</div><span id="gotoForm:j_idt1143"></span><script type="text/javascript">$(function(){PrimeFaces.focus('gotoForm:gotoApplicationNumber');});</script><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:3" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
			</div>
		</div>
		
		
		<div class="b-navigation-floater-bottom">
			<a class="fa fa-fw fa-arrow-up" style="text-decoration:none; float: right; color: #707070; font-size: 26px; margin: 20px;" onclick="wscrollToTop();"></a>
		</div>
		
	</div>


	
	
	
	<div id="popups">
		<div id="j_idt1166"><div id="j_idt1166:j_idt1168" class="ui-overlaypanel ui-widget ui-widget-content ui-overlay-hidden ui-corner-all ui-shadow ps-modal ps-modal--arrow-bottom ps-paginator-modal "><div class="ui-overlaypanel-content">
<form id="j_idt1166:j_idt1169" name="j_idt1166:j_idt1169" method="post" action="/search/zh/result.jsf" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt1166:j_idt1169" value="j_idt1166:j_idt1169" />
<input id="j_idt1166:j_idt1169:input" type="text" name="j_idt1166:j_idt1169:input" value="1" class="ps-paginator-modal--input" size="3" onkeyup="PrimeFaces.ab({s:this,e:&quot;keyup&quot;,p:&quot;j_idt1166:j_idt1169:input&quot;,u:&quot;j_idt1166:j_idt1169:button&quot;});" /><button id="j_idt1166:j_idt1169:button" name="j_idt1166:j_idt1169:button" class="b-button b-button--is-type_primary b-button--is-disabled ps-paginator-modal--button" onclick="PrimeFaces.ab({s:&quot;j_idt1166:j_idt1169:button&quot;,p:&quot;j_idt1166:j_idt1169&quot;,u:&quot;results-container @(.js-ps-global-messages)&quot;,onst:function(cfg){PF('w_paginatorChangePage').hide();;}});return false;" type="submit" disabled="disabled"><span class="ui-button-text ui-c">转至该页</span></button><script id="j_idt1166:j_idt1169:button_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1166_j_idt1169_button",{id:"j_idt1166:j_idt1169:button"});</script><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:4" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form></div></div><script id="j_idt1166:j_idt1168_s" type="text/javascript">$(function(){PrimeFaces.cw("OverlayPanel","w_paginatorChangePage",{id:"j_idt1166:j_idt1168",showEffect:"fade",hideEffect:"fade",my:"center bottom",at:"center top"});});</script>
		</div>
	</div>
	
	
	
	
	<div class="c-left-watermark">
	</div>
		
		<div class="b-step b-pointer-scope">
	 
			 <div class="b-step__content">
		    	
		    	<div class="b-step__content-top">

					
				<div class="refine-search-container"><div id="j_idt1203" class="ui-outputpanel ui-widget refine-search--input">
<form id="advancedSearchForm" name="advancedSearchForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="advancedSearchForm" value="advancedSearchForm" />
<div id="advancedSearchForm:j_idt1204" class="ui-messages ui-widget" aria-live="polite"></div><input id="advancedSearchForm:advancedSearchAssistant" type="checkbox" name="advancedSearchForm:advancedSearchAssistant" checked="checked" class="js-advanced-search-checkbox" style="display:none;" />
      			<div onkeydown="if (event.keyCode == 13) {$('.js-advanced-search-button').click(); return false;}">
	<div id="advancedSearchForm:advancedSearchInput">
		<div class="b-input b-input-text b-input--has-floatlabel                          ">
			<div class="b-input__table" style="width: 100%">
				<div class="b-input__table-td">
					<label class="b-input__label">
      						<div class="flex-container">
      							<div class="advq-ok advanced-search-icon">
      								<span class="fa fa-check-circle correct-color"></span>
      							</div>
      							
      							<div class="advq-err advanced-search-icon">
      								<span class="fa fa-times-circle error-color"></span>
      							</div>
      						
      							<div class="advanced-search-validation"></div>
      						</div>
					</label><input id="advancedSearchForm:advancedSearchInput:input" type="text" name="advancedSearchForm:advancedSearchInput:input" autocomplete="off" value="FP:(aaa)" class="b-input__input b-input__text-input                       js-advanced-search-input query-text" onkeydown="" onkeyup="" style="" tabindex="" title="" />
				</div><div id="advancedSearchForm:advancedSearchInput:buttons" class="b-input__button-wrapper"><button id="advancedSearchForm:advancedSearchInput:j_idt1208" name="advancedSearchForm:advancedSearchInput:j_idt1208" class="b-button-medium primary js-advanced-search-button" onclick="PrimeFaces.ab({s:&quot;advancedSearchForm:advancedSearchInput:j_idt1208&quot;,p:&quot;advancedSearchForm:advancedSearchInput:j_idt1208 advancedSearchForm&quot;,u:&quot;advancedSearchForm results-container @(.js-refine-search--commands, .js-ps-global-messages)&quot;,onco:function(xhr,status,args){$('.js-autocomplete-suggestions').hide();;}});return false;" title="检索" type="submit"><span class="ui-button-icon-left ui-icon ui-c b-icon b-icon--search"></span><span class="ui-button-text ui-c"></span></button><script id="advancedSearchForm:advancedSearchInput:j_idt1208_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_advancedSearchForm_advancedSearchInput_j_idt1208",{id:"advancedSearchForm:advancedSearchInput:j_idt1208"});</script></div>
	
		     </div><div id="advancedSearchForm:advancedSearchInput:error"></div><div id="advancedSearchForm:advancedSearchInput:info"></div>
	    </div>
    </div>
      			</div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:5" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form></div><div id="j_idt1255" class="ui-outputpanel ui-widget refine-search--commands js-refine-search--commands">
<form id="searchCriteriaForm" name="searchCriteriaForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="searchCriteriaForm" value="searchCriteriaForm" />

				
			<div class="search-commands-bar">
		
			
				<div class="search-criteria">
	
	<div class="ps-drawer-trigger b-pointer b-pointer--is-toggle" aria-controls="analysis_drawer"><a id="searchCriteriaForm:analysisTrigger" href="#" class="ui-commandlink ui-widget ps-link--has-icon" aria-label="分析" onclick="PrimeFaces.ab({s:&quot;searchCriteriaForm:analysisTrigger&quot;,p:&quot;searchCriteriaForm:analysisTrigger&quot;,u:&quot;analysisDrawerContainer @(.js-analysis-controls, .js-analysis-content)&quot;});return false;" title="分析">
							<span class="icon-wrapper analytics-icon"></span>
							<span class="results-count">5,638 个结果</span></a>
	</div>
	
	<div class="ps-drawer-trigger b-pointer b-pointer--is-toggle" aria-controls="commonSearchOptions_drawer">
			<span id="searchCriteriaForm:j_idt1264" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label ">
					专利局
				
			</span>
			
			<span class="ps-field--value ">
					all
				
			</span>
			</span>
			<span id="searchCriteriaForm:j_idt1285" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label ">
					语言
				
			</span>
			
			<span class="ps-field--value ">
					zh
				
			</span>
			</span>
			<span id="searchCriteriaForm:j_idt1306" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label ">
					词根提取
				
			</span>
			
			<span class="ps-field--value ">
					true
				
			</span>
			</span>
			<span id="searchCriteriaForm:j_idt1327" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label ">
					单一族成员
				
			</span>
			
			<span class="ps-field--value ">
					false
				
			</span>
			</span>
			<span id="searchCriteriaForm:j_idt1369" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label ">
					包括NPL
				
			</span>
			
			<span class="ps-field--value ">
					false
				
			</span>
			</span>
	</div>
					
				</div><div id="searchCriteriaForm:commansBar" class="js-multiDocumentDownloadEnabled">
		<div id="searchCriteriaForm:j_idt1393" class="ps-commands-bar ">
		<div class="ps-commands-bar--item"><span id="searchCriteriaForm:oldRss"><span class="icon-wrapper rss-icon"></span></span><div id="searchCriteriaForm:j_idt1403" class="ui-tooltip ui-widget ps-tooltip ui-tooltip-bottom"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all">只有拥有WIPO帐户才能生成RSS源</div></div><script id="searchCriteriaForm:j_idt1403_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_searchCriteriaForm_j_idt1403",{id:"searchCriteriaForm:j_idt1403",showEffect:"fade",hideEffect:"fade",hideDelay:1000,target:"searchCriteriaForm:oldRss",position:"bottom"});});</script>
		</div>
		<div class="ps-commands-bar--item">
	
	<div class="ps-drawer-trigger b-pointer b-pointer--is-toggle" aria-controls="queryTree_drawer"><a id="searchCriteriaForm:showQueryTree" href="#" class="ui-commandlink ui-widget ps-link--has-icon" aria-label="查看查询树" onclick="PrimeFaces.ab({s:&quot;searchCriteriaForm:showQueryTree&quot;,p:&quot;searchCriteriaForm:showQueryTree&quot;,u:&quot;queryData&quot;});return false;" title="查看查询树">
								<span class="icon-wrapper query-tree-icon"></span></a>
	</div>
		</div>
		<div class="ps-commands-bar--item"><a id="searchCriteriaForm:j_idt1429" href="#" class="ui-commandlink ui-widget ps-link--has-icon" aria-label="在结果列表旁显示详细信息" onclick="PrimeFaces.ab({s:&quot;searchCriteriaForm:j_idt1429&quot;,p:&quot;searchCriteriaForm:j_idt1429&quot;,u:&quot;@none&quot;});return false;" title="在结果列表旁显示详细信息">
							
							<span class="icon-wrapper columns-icon"></span></a>
		</div>
		</div></div>
			</div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:6" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
		<div id="saveQuery_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      ">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">保存查询</h2>
					<div class="b-drawer__title-actions"><button id="j_idt1446" name="j_idt1446" class="b-button js-close-pointer js-close-button" onclick="onDrawerClose(this);PrimeFaces.ab({s:&quot;j_idt1446&quot;,e:&quot;click&quot;,f:&quot;saveQueryForm&quot;,p:&quot;j_idt1446&quot;,u:&quot;saveQueryForm&quot;,g:false,rv:true});" type="button" aria-controls="saveQuery_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt1446_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1446",{id:"j_idt1446",behaviors:{click:function(ext,event) {PrimeFaces.ab({s:"j_idt1446",e:"click",f:"saveQueryForm",p:"j_idt1446",u:"saveQueryForm",g:false,rv:true},ext);}}});</script><button id="saveButton" name="saveButton" class="b-button b-button--is-type_primary" onclick="PrimeFaces.ab({s:&quot;saveButton&quot;,f:&quot;saveQueryForm&quot;,u:&quot;saveQueryForm&quot;});return false;" type="submit"><span class="ui-button-text ui-c">保存</span></button><script id="saveButton_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_saveButton",{id:"saveButton"});</script>
					</div>
			</div>

			<div class="ps-drawer--content">
<form id="saveQueryForm" name="saveQueryForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="saveQueryForm" value="saveQueryForm" />
<span id="saveQueryForm:j_idt1447"></span><script type="text/javascript">$(function(){PrimeFaces.focus('saveQueryForm:qName:input');});</script><input id="saveQueryForm:sdfile" type="hidden" name="saveQueryForm:sdfile" value="P20-MDFS25-41499-1" />
	<div id="saveQueryForm:j_idt1449" class="b-edit-panel">
		<div class="b-edit-panel__content">

	<div id="saveQueryForm:j_idt1450" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section ">
    
    <div id="saveQueryForm:j_idt1451" class="b-view-panel__slot w-slot">
	<div id="saveQueryForm:qName">
		<div class="b-input b-input-text b-input--has-floatlabel       b-input--is-required                   ">
			<div class="b-input__table" style="width: 100%">
				<div class="b-input__table-td">
					<label class="b-input__label">查询名称
					</label><input id="saveQueryForm:qName:input" type="text" name="saveQueryForm:qName:input" autocomplete="off" class="b-input__input b-input__text-input                       " onkeydown="" onkeyup="" style="" tabindex="" title="" />
				</div>
	
		     </div><div id="saveQueryForm:qName:error"></div><div id="saveQueryForm:qName:info"></div>
	    </div>
    </div>
	<div id="saveQueryForm:qQuery">
		<div class="b-input b-input-text-area b-input--has-floatlabel      b-input--is-required                      ">
	
			<div class="b-input__table">
				<div class="b-input__table-td">
	
					<label class="b-input__label"><span class="ie-bg-label">查询文字</span></label><textarea id="saveQueryForm:qQuery:input" name="saveQueryForm:qQuery:input" cols="20" rows="3" tabindex="" maxlength="2147483647" style="overflow-y: auto; text-transform:query-text" aria-required="true" class="ui-inputfield ui-inputtextarea ui-widget ui-state-default ui-corner-all b-input__text-area-input query-text">FP:(aaa)</textarea><script id="saveQueryForm:qQuery:input_s" type="text/javascript">$(function(){PrimeFaces.cw("InputTextarea","widget_saveQueryForm_qQuery_input",{id:"saveQueryForm:qQuery:input",autoResize:false,counter:"saveQueryForm:qQuery:display",counterTemplate:"剩余 {0} 个字符。"});});</script>
	
				</div>
			</div><div id="saveQueryForm:qQuery:error"></div><div id="saveQueryForm:qQuery:info"></div>
		</div>
	</div>
	<div id="saveQueryForm:qPrivate">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="saveQueryForm:qPrivate:input" type="checkbox" name="saveQueryForm:qPrivate:input" checked="checked" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;saveQueryForm:qPrivate:input&quot;});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">私人查询
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="saveQueryForm:qPrivate:error"></div><div id="saveQueryForm:qPrivate:info"></div>
	</div>
	</div><div id="saveQueryForm:j_idt1514" class="ui-tooltip ui-widget ps-tooltip ui-tooltip-bottom" style="max-width: 500px;"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><span style="white-space: normal;">私人查询仅在您登录后对您可见，而不能在RSS 订阅中使用</span></div></div><script id="saveQueryForm:j_idt1514_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_saveQueryForm_j_idt1514",{id:"saveQueryForm:j_idt1514",showEffect:"fade",hideEffect:"fade",target:"saveQueryForm:qPrivate",position:"bottom"});});</script>
	</div>
			
		</div>
	</div>
		</div>
	</div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:7" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
			</div>
		</div>
		<div id="queryTree_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      queryTree-drawer">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">查询树</h2>
					<div class="b-drawer__title-actions"><button id="j_idt1557" name="j_idt1557" class="b-button js-close-pointer js-close-button" onclick="onDrawerClose(this);" type="button" aria-controls="queryTree_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt1557_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1557",{id:"j_idt1557"});</script>
					</div>
			</div>

			<div class="ps-drawer--content"><div id="queryData" class="ui-outputpanel ui-widget queryTree-outputpanel"></div>
			</div>
		</div>
		<div id="commonSearchOptions_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      ">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">优化选项</h2>
					<div class="b-drawer__title-actions"><button id="j_idt1573" name="j_idt1573" class="b-button js-close-pointer js-close-button" onclick="onDrawerClose(this);" type="button" aria-controls="commonSearchOptions_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt1573_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1573",{id:"j_idt1573"});</script><button id="j_idt1574" name="j_idt1574" class="b-button b-button--is-type_primary" onclick="PrimeFaces.ab({s:&quot;j_idt1574&quot;,f:&quot;commonSearchOptionsForm&quot;,p:&quot;j_idt1574 commonSearchOptionsForm&quot;,u:&quot;results-container @(.js-refine-search--commands)&quot;});return false;" type="submit"><span class="ui-button-text ui-c">检索</span></button><script id="j_idt1574_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1574",{id:"j_idt1574"});</script>
					</div>
			</div>

			<div class="ps-drawer--content">
<form id="commonSearchOptionsForm" name="commonSearchOptionsForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="commonSearchOptionsForm" value="commonSearchOptionsForm" />

	<div id="commonSearchOptionsForm:j_idt1575" class="b-edit-panel">
		<div class="b-edit-panel__content">

	<div id="commonSearchOptionsForm:j_idt1576" class="b-edit-panel__section-group">
		<div class="b-edit-panel__section ">
    
    <div id="commonSearchOptionsForm:j_idt1577" class="b-view-panel__slot w-slot"><span id="commonSearchOptionsForm:searchOfficesOption" class="js-offices-option">
	<div class="ps-office">
		<div class="ps-office--input" onclick="$('.ps-office--options').toggle();">
			<div class="ps-office--text">
				<div class="ps-office--label">专利局</div><span id="commonSearchOptionsForm:office:officeValue" class="ps-office--value js-ps-office--value">全部</span>
			</div>
			<button class="ps-office--button" type="button">
				<span class="icon-wrapper small-arrow-down-icon"></span>
			</button>
		</div>
		
		<div class="ps-office--options" style="display: none;"><div id="commonSearchOptionsForm:office:officeOptions" class="js-ps-office--options">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1581:j_idt1583" type="checkbox" name="commonSearchOptionsForm:office:j_idt1581:j_idt1583" checked="checked" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1581:j_idt1583&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">全部</label>
	</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:0:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:0:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:0:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">专利合作条约</label>
	</div>
						
					</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:1:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:1:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">非洲</label>
	</div>
					
							<div class="ps-office--options--children"><div id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595" class="ui-datagrid ui-widget no-border ps-office--options--children--grid"><div id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595_content" class="ui-datagrid-content ui-widget-content ui-grid ui-grid-responsive"><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:0:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:0:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:0:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">南非</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:1:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:1:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:1:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">肯尼亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:2:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:2:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:1:j_idt1595:2:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">非洲地区知识产权组织 (ARIPO)</label>
	</div></div></div></div></div><script id="commonSearchOptionsForm:office:j_idt1586:1:j_idt1595_s" type="text/javascript">PrimeFaces.cw("DataGrid","widget_commonSearchOptionsForm_office_j_idt1586_1_j_idt1595",{id:"commonSearchOptionsForm:office:j_idt1586:1:j_idt1595"});</script>
					
							</div>
						
					</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:2:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:2:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">ARABPAT</label>
	</div>
					
							<div class="ps-office--options--children"><div id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595" class="ui-datagrid ui-widget no-border ps-office--options--children--grid"><div id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595_content" class="ui-datagrid-content ui-widget-content ui-grid ui-grid-responsive"><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:0:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:0:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:0:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">埃及</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:1:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:1:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:1:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">摩洛哥</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:2:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:2:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:2:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">沙特阿拉伯</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:3:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:3:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:3:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">突尼斯</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:4:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:4:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:2:j_idt1595:4:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">约旦</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4"></div></div></div></div><script id="commonSearchOptionsForm:office:j_idt1586:2:j_idt1595_s" type="text/javascript">PrimeFaces.cw("DataGrid","widget_commonSearchOptionsForm_office_j_idt1586_2_j_idt1595",{id:"commonSearchOptionsForm:office:j_idt1586:2:j_idt1595"});</script>
					
							</div>
						
					</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:3:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:3:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:3:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">美洲</label>
	</div>
					
							<div class="ps-office--options--children"><div id="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595" class="ui-datagrid ui-widget no-border ps-office--options--children--grid"><div id="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595_content" class="ui-datagrid-content ui-widget-content ui-grid ui-grid-responsive"><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595:0:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595:0:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:3:j_idt1595:0:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">加拿大</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595:1:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595:1:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:3:j_idt1595:1:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">美国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4"></div></div></div></div><script id="commonSearchOptionsForm:office:j_idt1586:3:j_idt1595_s" type="text/javascript">PrimeFaces.cw("DataGrid","widget_commonSearchOptionsForm_office_j_idt1586_3_j_idt1595",{id:"commonSearchOptionsForm:office:j_idt1586:3:j_idt1595"});</script>
					
							</div>
						
					</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">LATIPAT</label>
	</div>
					
							<div class="ps-office--options--children"><div id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595" class="ui-datagrid ui-widget no-border ps-office--options--children--grid"><div id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595_content" class="ui-datagrid-content ui-widget-content ui-grid ui-grid-responsive"><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:0:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:0:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:0:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">乌拉圭</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:1:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:1:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:1:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">危地马拉</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:2:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:2:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:2:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">厄瓜多尔</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:3:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:3:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:3:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">古巴</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:4:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:4:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:4:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">哥伦比亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:5:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:5:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:5:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">哥斯达黎加</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:6:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:6:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:6:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">墨西哥</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:7:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:7:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:7:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">多米尼加共和国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:8:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:8:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:8:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">尼加拉瓜</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:9:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:9:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:9:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">巴拿马</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:10:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:10:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:10:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">巴西</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:11:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:11:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:11:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">智利</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:12:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:12:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:12:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">洪都拉斯</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:13:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:13:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:13:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">秘鲁</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:14:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:14:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:14:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">萨尔瓦多</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:15:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:15:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:4:j_idt1595:15:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">阿根廷</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4"></div><div class="ui-datagrid-column ui-grid-col-4"></div></div></div></div><script id="commonSearchOptionsForm:office:j_idt1586:4:j_idt1595_s" type="text/javascript">PrimeFaces.cw("DataGrid","widget_commonSearchOptionsForm_office_j_idt1586_4_j_idt1595",{id:"commonSearchOptionsForm:office:j_idt1586:4:j_idt1595"});</script>
					
							</div>
						
					</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">亚洲和欧洲</label>
	</div>
					
							<div class="ps-office--options--children"><div id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595" class="ui-datagrid ui-widget no-border ps-office--options--children--grid"><div id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595_content" class="ui-datagrid-content ui-widget-content ui-grid ui-grid-responsive"><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:0:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:0:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:0:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">中国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:1:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:1:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:1:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">丹麦</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:2:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:2:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:2:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">以色列</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:3:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:3:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:3:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">俄罗斯</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:4:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:4:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:4:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">俄罗斯（苏联数据）</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:5:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:5:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:5:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">保加利亚</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:6:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:6:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:6:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">克罗地亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:7:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:7:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:7:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">匈牙利</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:8:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:8:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:8:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">印度</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:9:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:9:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:9:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">哈萨克斯坦</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:10:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:10:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:10:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">土耳其</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:11:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:11:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:11:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">塞尔维亚</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:12:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:12:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:12:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">奥地利</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:13:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:13:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:13:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">巴林</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:14:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:14:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:14:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">希腊</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:15:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:15:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:15:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">德国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:16:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:16:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:16:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">德国（东德数据）</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:17:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:17:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:17:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">意大利</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:18:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:18:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:18:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">拉脱维亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:19:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:19:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:19:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">挪威</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:20:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:20:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:20:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">捷克共和国</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:21:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:21:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:21:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">捷克斯洛伐克</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:22:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:22:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:22:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">摩纳哥</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:23:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:23:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:23:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">斯洛伐克</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:24:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:24:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:24:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">新西兰</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:25:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:25:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:25:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">日本</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:26:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:26:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:26:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">格鲁吉亚</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:27:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:27:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:27:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">欧亚专利局 (EAPO)</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:28:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:28:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:28:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">欧洲专利局 (EPO)</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:29:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:29:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:29:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">比利时</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:30:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:30:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:30:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">法国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:31:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:31:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:31:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">波兰</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:32:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:32:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:32:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">海湾阿拉伯国家合作委员会专利局 (GCC)</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:33:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:33:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:33:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">澳大利亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:34:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:34:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:34:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">爱沙尼亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:35:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:35:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:35:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">瑞典</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:36:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:36:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:36:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">瑞士</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:37:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:37:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:37:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">立陶宛</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:38:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:38:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:38:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">罗马尼亚</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:39:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:39:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:39:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">芬兰</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:40:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:40:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:40:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">英国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:41:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:41:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:41:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">荷兰</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:42:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:42:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:42:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">葡萄牙</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:43:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:43:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:43:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">西班牙</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:44:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:44:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:44:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">阿拉伯联合酋长国</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:45:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:45:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:45:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">韩国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:46:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:46:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:5:j_idt1595:46:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">马耳他</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4"></div></div></div></div><script id="commonSearchOptionsForm:office:j_idt1586:5:j_idt1595_s" type="text/javascript">PrimeFaces.cw("DataGrid","widget_commonSearchOptionsForm_office_j_idt1586_5_j_idt1595",{id:"commonSearchOptionsForm:office:j_idt1586:5:j_idt1595"});</script>
					
							</div>
						
					</div>
					
					<div class="ps-office--options--group">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1588:j_idt1590" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1588:j_idt1590" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1588:j_idt1590&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">东盟</label>
	</div>
					
							<div class="ps-office--options--children"><div id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595" class="ui-datagrid ui-widget no-border ps-office--options--children--grid"><div id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595_content" class="ui-datagrid-content ui-widget-content ui-grid ui-grid-responsive"><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:0:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:0:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:0:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">印度尼西亚</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:1:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:1:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:1:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">文莱达鲁萨兰国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:2:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:2:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:2:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">新加坡</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:3:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:3:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:3:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">柬埔寨</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:4:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:4:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:4:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">泰国</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:5:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:5:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:5:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">老挝人民民主共和国</label>
	</div></div></div><div class="ui-grid-row"><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:6:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:6:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:6:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">菲律宾</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:7:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:7:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:7:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">越南</label>
	</div></div><div class="ui-datagrid-column ui-grid-col-4">
	<div class="ps-office--options--item"><input id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:8:j_idt1596:j_idt1598" type="checkbox" name="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:8:j_idt1596:j_idt1598" class="ps-office--options--checkbox" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:office:j_idt1586:6:j_idt1595:8:j_idt1596:j_idt1598&quot;,u:&quot;@(.js-ps-office--value) @(.js-ps-office--options)&quot;,onco:function(xhr,status,args){;}});" /><label class="ps-office--options--label">马来西亚</label>
	</div></div></div></div></div><script id="commonSearchOptionsForm:office:j_idt1586:6:j_idt1595_s" type="text/javascript">PrimeFaces.cw("DataGrid","widget_commonSearchOptionsForm_office_j_idt1586_6_j_idt1595",{id:"commonSearchOptionsForm:office:j_idt1586:6:j_idt1595"});</script>
					
							</div>
						
					</div></div>
		</div>
	</div></span><span id="commonSearchOptionsForm:searchTextLanguageOption" class="js-searchTextLanguage-option js-tooltip-help">
	<div id="commonSearchOptionsForm:queryLanguage">
		<div class="b-input b-input-dropdown b-input--has-floatlabel b-input--is-select                         ">
	
			<div class="b-input__table">
				<div class="b-input__table-td b-input__table-td__dropdown">
					<label class="b-input__label b-input__label">语言
					</label><select id="commonSearchOptionsForm:queryLanguage:input" name="commonSearchOptionsForm:queryLanguage:input" class="b-input__input b-input__dropdown-input" size="1" onselect="" tabindex="" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;commonSearchOptionsForm:queryLanguage:input&quot;,onco:function(xhr,status,args){;}});">	<option value="zh" selected="selected">中文</option>
	<option value="da">丹麦语</option>
	<option value="ru">俄语</option>
	<option value="bg">保加利亚语</option>
	<option value="hr">克罗地亚文</option>
	<option value="hu">匈牙利语</option>
	<option value="id">印度尼西亚语</option>
	<option value="kk">哈萨克文</option>
	<option value="tr">土耳其文</option>
	<option value="sr">塞尔维亚文</option>
	<option value="he">希伯来语</option>
	<option value="el">希腊语</option>
	<option value="de">德语</option>
	<option value="it">意大利语</option>
	<option value="lv">拉脱维亚语</option>
	<option value="no">挪威语</option>
	<option value="cs">捷克文</option>
	<option value="sk">斯洛伐克文</option>
	<option value="ja">日语</option>
	<option value="ge">格鲁吉亚语</option>
	<option value="fr">法语</option>
	<option value="pl">波兰语</option>
	<option value="th">泰语</option>
	<option value="et">爱沙尼亚语</option>
	<option value="sv">瑞典语</option>
	<option value="lt">立陶宛语 </option>
	<option value="ro">罗马尼亚语</option>
	<option value="lo">老挝语</option>
	<option value="fi">芬兰文</option>
	<option value="en">英语</option>
	<option value="nl">荷兰语</option>
	<option value="pt">葡萄牙语</option>
	<option value="es">西班牙语</option>
	<option value="vn">越南语</option>
	<option value="ar">阿拉伯语</option>
	<option value="ko">韩语</option>
	<option value="ms">马来语</option>
	<option value="kh">高棉语</option>
</select>
	
				</div>
			</div><div id="commonSearchOptionsForm:queryLanguage:error"></div><div id="commonSearchOptionsForm:queryLanguage:info"></div>
		</div>
	</div><div id="commonSearchOptionsForm:j_idt1623" class="ui-tooltip ui-widget ps-tooltip ui-tooltip-bottom"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><span>指明检索关键词的语言</span></div></div><script id="commonSearchOptionsForm:j_idt1623_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_commonSearchOptionsForm_j_idt1623",{id:"commonSearchOptionsForm:j_idt1623",showEffect:"fade",hideEffect:"fade",target:"commonSearchOptionsForm:queryLanguage",position:"bottom"});});</script></span><span id="commonSearchOptionsForm:searchStemmingOption" class="js-stemming-option js-tooltip-help">
	<div id="commonSearchOptionsForm:stemmingOption">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="commonSearchOptionsForm:stemmingOption:input" type="checkbox" name="commonSearchOptionsForm:stemmingOption:input" checked="checked" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;commonSearchOptionsForm:stemmingOption:input&quot;,onco:function(xhr,status,args){;}});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">词根提取
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="commonSearchOptionsForm:stemmingOption:error"></div><div id="commonSearchOptionsForm:stemmingOption:info"></div>
	</div>
	</div><div id="commonSearchOptionsForm:j_idt1663" class="ui-tooltip ui-widget ps-tooltip ui-tooltip-bottom"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><span><b>词根提取</b>功能可将屈折词还原回词根形式。<br/>例如，<b>fishing</b>、<b>fished</b>、<b>fish</b> 和 <b>fisher</b> 这些词都会还原回词根 <b>fish</b>，<br/>因此检索 <b>fisher</b> 即可获得各种变形的所有结果</span></div></div><script id="commonSearchOptionsForm:j_idt1663_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_commonSearchOptionsForm_j_idt1663",{id:"commonSearchOptionsForm:j_idt1663",showEffect:"fade",hideEffect:"fade",target:"commonSearchOptionsForm:stemmingOption",position:"bottom"});});</script></span><span id="commonSearchOptionsForm:searchSingleFamilyMemberOption" class="js-singleFamilyMember-option js-tooltip-help">
	<div id="commonSearchOptionsForm:singleFamilyMemberOption">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="commonSearchOptionsForm:singleFamilyMemberOption:input" type="checkbox" name="commonSearchOptionsForm:singleFamilyMemberOption:input" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;commonSearchOptionsForm:singleFamilyMemberOption:input&quot;,onco:function(xhr,status,args){;}});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">单一族成员
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="commonSearchOptionsForm:singleFamilyMemberOption:error"></div><div id="commonSearchOptionsForm:singleFamilyMemberOption:info"></div>
	</div>
	</div><div id="commonSearchOptionsForm:j_idt1682" class="ui-tooltip ui-widget ps-tooltip ui-tooltip-bottom"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><span>仅返回专利族的一个成员</span></div></div><script id="commonSearchOptionsForm:j_idt1682_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_commonSearchOptionsForm_j_idt1682",{id:"commonSearchOptionsForm:j_idt1682",showEffect:"fade",hideEffect:"fade",target:"commonSearchOptionsForm:singleFamilyMemberOption",position:"bottom"});});</script></span><span id="commonSearchOptionsForm:searchIncludeNPLOption" class="js-includeNPL-option js-tooltip-help">
	<div id="commonSearchOptionsForm:includeNPLOption">
		
		<div class="b-input b-input-checkbox                    ">
			<div class="b-input__checkbox-input">
				<fieldset class="b-input__checkbox-group">
      				<div class="b-input__table">
						<label class="b-input__checkbox-item b-input__table-tr"><input id="commonSearchOptionsForm:includeNPLOption:input" type="checkbox" name="commonSearchOptionsForm:includeNPLOption:input" tabindex="" onclick="PrimeFaces.ab({s:this,e:&quot;click&quot;,p:&quot;commonSearchOptionsForm:includeNPLOption:input&quot;,onco:function(xhr,status,args){;}});" />
							<span class="b-input__checkbox-item-text b-input__table-td b-input__checkbox-input-label">包括NPL
							</span>
						</label>
					</div>
				</fieldset>
			</div><div id="commonSearchOptionsForm:includeNPLOption:error"></div><div id="commonSearchOptionsForm:includeNPLOption:info"></div>
	</div>
	</div><div id="commonSearchOptionsForm:j_idt1701" class="ui-tooltip ui-widget ps-tooltip ui-tooltip-bottom"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all">在结果中包括非专利文献</div></div><script id="commonSearchOptionsForm:j_idt1701_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_commonSearchOptionsForm_j_idt1701",{id:"commonSearchOptionsForm:j_idt1701",showEffect:"fade",hideEffect:"fade",target:"commonSearchOptionsForm:includeNPLOption",position:"bottom"});});</script></span>
	</div>
			
		</div>
	</div>
		</div>
	</div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:8" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
			</div>
		</div>
		<div id="edit_query_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      ">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">完整查询</h2>
					<div class="b-drawer__title-actions"><button id="j_idt1742" name="j_idt1742" class="b-button js-close-pointer js-close-button" onclick="onDrawerClose(this);" type="button" aria-controls="edit_query_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt1742_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1742",{id:"j_idt1742"});</script><button id="j_idt1743" name="j_idt1743" class="b-button b-button--is-type_primary" onclick="document.location.href='advancedSearch.jsf'" title="在“高级检索”表中编辑查询" type="button"><span class="ui-button-text ui-c">编辑</span></button><script id="j_idt1743_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1743",{id:"j_idt1743"});</script>
					</div>
			</div>

			<div class="ps-drawer--content">
		<div id="fullQueryPanel" class="ps-panel ">
			<div class="ps-panel--content font-size--small">
			<span class="query-text">FP:(aaa)</span>
			</div>
		</div>
			</div>
		</div>
		<div id="keyboard_shortcuts_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      ">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">并排视图快捷键</h2>
					<div class="b-drawer__title-actions"><button id="j_idt1758" name="j_idt1758" class="b-button js-close-pointer js-close-button" onclick="onDrawerClose(this);" type="button" aria-controls="keyboard_shortcuts_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt1758_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1758",{id:"j_idt1758"});</script>
					</div>
			</div>

			<div class="ps-drawer--content"><div id="j_idt1759" class="ui-outputpanel ui-widget hotkeys-container flex-container">
		<div id="j_idt1760" class="ps-panel ">
				<div class="ps-panel--header">
						<div class="ps-panel--header--text">一般快捷键
						</div>
				</div>
			<div class="ps-panel--content "><table class="hotkeys-list no-border">
<tbody>
<tr>
<td class="content-dir-opposite">
					<span class="hotkeyLabel">转到检索输入栏</span></td>
<td><div id="j_idt1764" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd>SHIFT</kbd> + <kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-up-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">转到结果（选定结果）</span></td>
<td><div id="j_idt1767" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd>SHIFT</kbd> + <kbd><span class="icon-wrapper icon-wrapper--is-16  arrow-left-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">转到详细信息（选定标签）</span></td>
<td><div id="j_idt1770" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd>SHIFT</kbd> + <kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-right-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">

					<span class="hotkeyLabel">转到下一页</span></td>
<td><div id="j_idt1773" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd><span class="icon-wrapper icon-wrapper--is-16  arrow-right-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">转到上一页</span></td>
<td><div id="j_idt1776" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd><span class="icon-wrapper icon-wrapper--is-16  arrow-left-icon"></span></kbd></div></td>
</tr>
</tbody>
</table>

			</div>
		</div>
		<div id="j_idt1786" class="ps-panel ">
				<div class="ps-panel--header">
						<div class="ps-panel--header--text">结果（首先，执行“转到结果”）
						</div>
				</div>
			<div class="ps-panel--content "><table class="hotkeys-list no-border">
<tbody>
<tr>
<td class="content-dir-opposite">
					<span class="hotkeyLabel">转到下一个结果/图像</span></td>
<td><div id="j_idt1790" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-up-icon"></span></kbd> / <kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-right-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">转到上一个结果/图像</span></td>
<td><div id="j_idt1793" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-down-icon"></span></kbd> / <kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-left-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">向上滚动</span></td>
<td><div id="j_idt1796" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>Page Up</kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">向下滚动</span></td>
<td><div id="j_idt1800" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>Page Down</kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">滚动至顶部</span></td>
<td><div id="j_idt1803" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd>Home</kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
										
					<span class="hotkeyLabel">滚动至底部</span></td>
<td><div id="j_idt1806" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd>CTRL</kbd> + <kbd>End</kbd></div></td>
</tr>
</tbody>
</table>

			</div>
		</div>
		<div id="j_idt1816" class="ps-panel ">
				<div class="ps-panel--header">
						<div class="ps-panel--header--text">详细信息（首先，执行“转到详细信息”）
						</div>
				</div>
			<div class="ps-panel--content "><table class="hotkeys-list no-border">
<tbody>
<tr>
<td class="content-dir-opposite">
					<span class="hotkeyLabel">转到下一个标签</span></td>
<td><div id="j_idt1820" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-right-icon"></span></kbd></div></td>
</tr>
<tr>
<td class="content-dir-opposite">
					
					<span class="hotkeyLabel">转到上一个标签</span></td>
<td><div id="j_idt1823" class="ui-outputpanel ui-widget hotkeyCombination">
						<kbd><span class="icon-wrapper icon-wrapper--is-16 arrow-left-icon"></span></kbd></div></td>
</tr>
</tbody>
</table>

			</div>
		</div></div>
			</div>
		</div><div id="analysisDrawerContainer" class="ui-outputpanel ui-widget js-analysis-drawer">
		<div id="analysis_drawer" class="ps-drawer b-drawer b-workbench-filter-advanced     wf-drawer-fix-position      ">

			<div class="b-drawer__step">
				<h2 class="b-drawer__title">分析</h2>
					<div class="b-drawer__title-actions"><button id="j_idt1842" name="j_idt1842" class="b-button js-close-pointer" onclick="PrimeFaces.ab({s:&quot;j_idt1842&quot;,f:&quot;analysisForm&quot;,u:&quot;analysisDrawerContainer @(.js-analysis-controls, .js-analysis-content)&quot;,g:false});return false;" type="submit" aria-controls="analysis_drawer"><span class="ui-button-text ui-c">关闭</span></button><script id="j_idt1842_s" type="text/javascript">PrimeFaces.cw("CommandButton","widget_j_idt1842",{id:"j_idt1842"});</script>
					</div>
			</div>

			<div class="ps-drawer--content">
<form id="analysisForm" name="analysisForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="analysisForm" value="analysisForm" />
<div id="analysisForm:analysis_controls" class="ui-outputpanel ui-widget js-analysis-controls"></div><div id="analysisForm:analysis_content" class="ui-outputpanel ui-widget js-analysis-content"><script id="analysisForm:loadFacetsPoll_s" type="text/javascript">$(function(){PrimeFaces.cw("Poll","widget_analysisForm_loadFacetsPoll",{id:"analysisForm:loadFacetsPoll",frequency:1,autoStart:false,fn:function(){PrimeFaces.ab({s:"analysisForm:loadFacetsPoll",f:"analysisForm",u:"@(.js-analysis-content)",g:false});}});});</script>
		<div id="analysisForm:j_idt1848" class="ps-processing">
			
			<div class="ps-processing--animation js-processing"><img id="analysisForm:j_idt1848:j_idt1856" src="/search/javax.faces.resource/images/<EMAIL>?ln=w" alt="" />
			</div>
		</div></div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:9" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
			</div>
		</div></div></div>
				</div><div id="results-container" class="ui-outputpanel ui-widget "><div id="results-section" class="ui-outputpanel ui-widget  js-results-section">
<form id="resultListCommandsForm" name="resultListCommandsForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="resultListCommandsForm" value="resultListCommandsForm" />

						
		<div class="results-commands-bar ">
		<div id="resultListCommandsForm:j_idt1958" class="ps-commands-bar side-commands">
		<div class="ps-commands-bar--item">
	<div class="ps-plain-select">
			<label class="ps-plain-select--label">排序：
			</label><select id="resultListCommandsForm:sort:input" name="resultListCommandsForm:sort:input" class="ps-plain-select--input " size="1" onselect="" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;resultListCommandsForm:sort:input&quot;,u:&quot;results-container @(.js-refine-search--commands)&quot;});">	<option value="-score" selected="selected">相关性</option>
	<option value="-DP">公布日降序</option>
	<option value="+DP">公布日升序</option>
	<option value="-AD">申请日降序</option>
	<option value="+AD">申请日升序</option>
</select>
		<select class="ps-plain-select--input-helper">
			<option></option>
		</select>
		
	</div>
		</div>
		<div class="ps-commands-bar--item">
	<div class="ps-plain-select">
			<label class="ps-plain-select--label">每页：
			</label><select id="resultListCommandsForm:perPage:input" name="resultListCommandsForm:perPage:input" class="ps-plain-select--input " size="1" onselect="" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;resultListCommandsForm:perPage:input&quot;,u:&quot;results-container&quot;});">	<option value="10" selected="selected">10</option>
	<option value="50">50</option>
	<option value="100">100</option>
	<option value="200">200</option>
</select>
		<select class="ps-plain-select--input-helper">
			<option></option>
		</select>
		
	</div>
		</div>
		<div class="ps-commands-bar--item">
	<div class="ps-plain-select">
			<label class="ps-plain-select--label">查看：
			</label><select id="resultListCommandsForm:viewType:input" name="resultListCommandsForm:viewType:input" class="ps-plain-select--input " size="1" onselect="" onchange="PrimeFaces.ab({s:this,e:&quot;change&quot;,p:&quot;resultListCommandsForm:viewType:input&quot;,u:&quot;results-container&quot;});">	<option value="SIMPLE_VIEW">简洁</option>
	<option value="DOUBLE_VIEW">双</option>
	<option value="ALL_VIEW" selected="selected">全文</option>
	<option value="ALL_VIEW_IMAGE">全文 + 图像</option>
	<option value="IMAGE_ONLY">图像</option>
	<option value="MULTI_LANGS">多列</option>
</select>
		<select class="ps-plain-select--input-helper">
			<option></option>
		</select>
		
	</div>
		</div>
		</div>
		<div class="ps-paginator"><span id="resultListCommandsForm:j_idt1998" class="ui-commandlink ui-widget ui-state-disabled ps-link--has-icon js-paginator-prev">
				<span class="icon-wrapper chevron-left-icon"></span></span>
			
			
			<div class="ps-paginator--page"><a id="resultListCommandsForm:invalidPageNumber" href="#" class="ui-commandlink ui-widget ps-paginator--page--select" aria-label="单击以转至特定页面" onclick="PF('w_paginatorChangePage').show('resultListCommandsForm:invalidPageNumber');return false;;PrimeFaces.ab({s:&quot;resultListCommandsForm:invalidPageNumber&quot;});return false;" title="单击以转至特定页面">
					<span class="ps-paginator--page--value"><div id="resultListCommandsForm:j_idt2002" aria-live="polite" class="ps-paginator--page--error ui-message"></div><span id="resultListCommandsForm:pageNumber">1</span> / 564
						<span class="ps-paginator--page--icon icon-wrapper icon-wrapper--is-16 small-arrow-down-icon"></span>
					</span></a>
			</div><a id="resultListCommandsForm:j_idt2007" href="#" class="ui-commandlink ui-widget ps-link--has-icon js-paginator-next" aria-label="下一页" onclick="PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2007&quot;,p:&quot;resultListCommandsForm:j_idt2007&quot;,u:&quot;results-container @(.js-ps-global-messages)&quot;,onco:function(xhr,status,args){createCustomEvent('paginator.changePage');;}});return false;" title="下一页">
				<span class="icon-wrapper chevron-right-icon"></span></a>
		</div>
		<div id="resultListCommandsForm:j_idt2010" class="ps-commands-bar side-commands">
		<div class="ps-commands-bar--item"><span id="resultListCommandsForm:resultListTapta" class="js-tapta-menu">
		<div id="google_translate_element-rs" style="display:none">
		   <div style="display: flex; flex-wrap: nowrap">
               
               <span class="notranslate" style="color:black">Translated by Google</span>
               <span class="icon-wrapper close-icon" onclick="location.reload()"></span>
            </div>
		</div><div id="resultListCommandsForm:j_idt2025" class="ui-menu ui-menubar ui-widget ui-widget-content ui-corner-all ui-helper-clearfix mtMenuResultList" role="menubar"><div tabindex="0" class="ui-helper-hidden-accessible"></div><ul class="ui-menu-list ui-helper-reset"><li class="ui-widget ui-menuitem ui-corner-all ui-menu-parent" role="menuitem" aria-haspopup="true"><a href="#" class="ui-menuitem-link ui-submenu-link ui-corner-all" tabindex="-1"><span class="ui-menuitem-text">机器翻译</span><span class="ui-icon ui-icon-triangle-1-s"></span></a><ul class="ui-widget-content ui-menu-list ui-corner-all ui-helper-clearfix ui-menu-child ui-shadow" role="menu"><li class="ui-widget ui-menuitem ui-corner-all ui-menu-parent" role="menuitem" aria-haspopup="true"><a href="#" class="ui-menuitem-link ui-submenu-link ui-corner-all" tabindex="-1"><span class="ui-menuitem-text">WIPO Translate</span><span class="ui-icon ui-icon-triangle-1-e"></span></a><ul class="ui-widget-content ui-menu-list ui-corner-all ui-helper-clearfix ui-menu-child ui-shadow" role="menu"><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('zh');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_0&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">中文</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('ru');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_1&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">俄语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('sr');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_2&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">塞尔维亚文</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('de');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_3&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">德语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('it');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_4&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">意大利语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('cs');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_5&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">捷克文</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('sk');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_6&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">斯洛伐克文</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('ja');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_7&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">日语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('fr');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_8&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">法语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('pl');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_9&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">波兰语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('fi');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_10&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">芬兰文</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('en');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_11&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">英语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('nl');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_12&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">荷兰语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('pt');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_13&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">葡萄牙语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('es');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_14&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">西班牙语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('ar');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_15&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">阿拉伯语</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all mtMenu-langugae" href="#" onclick="taptaTranslateResultList('ko');PrimeFaces.ab({s:&quot;resultListCommandsForm:j_idt2025&quot;,g:false,pa:[{name:&quot;resultListCommandsForm:j_idt2025_menuid&quot;,value:&quot;0_0_16&quot;}],f:&quot;resultListCommandsForm&quot;});return false;"><span class="ui-menuitem-text">韩语</span></a></li></ul></li></ul></li></ul></div><script id="resultListCommandsForm:j_idt2025_s" type="text/javascript">PrimeFaces.cw("Menubar","widget_resultListCommandsForm_j_idt2025",{id:"resultListCommandsForm:j_idt2025",autoDisplay:true,toggleEvent:"click"});</script></span>
		</div>
		</div>
		</div><div id="resultListCommandsForm:downloadMenu" class="ui-menu ui-menu-dynamic ui-widget ui-widget-content ui-corner-all ui-helper-clearfix ui-shadow ps-menu-toggleable--menu" role="menu"><div tabindex="0" class="ui-helper-hidden-accessible"></div><ul class="ui-menu-list ui-helper-reset"><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.addSubmitParam('resultListCommandsForm',{'resultListCommandsForm:j_idt2031':'resultListCommandsForm:j_idt2031'}).submit('resultListCommandsForm');return false;PrimeFaces.onPost();"><span class="ui-menuitem-text">100个结果</span></a></li><li class="ui-menuitem ui-widget ui-corner-all" role="menuitem"><a tabindex="-1" class="ui-menuitem-link ui-corner-all" href="#" onclick="PrimeFaces.addSubmitParam('resultListCommandsForm',{'resultListCommandsForm:j_idt2032':'resultListCommandsForm:j_idt2032'}).submit('resultListCommandsForm');return false;PrimeFaces.onPost();"><span class="ui-menuitem-text">10,000个结果</span></a></li></ul></div><script id="resultListCommandsForm:downloadMenu_s" type="text/javascript">$(function(){PrimeFaces.cw("PlainMenu","widget_resultListCommandsForm_downloadMenu",{id:"resultListCommandsForm:downloadMenu",overlay:true,my:"right top",at:"right bottom",trigger:"@(.js-trigger-downloadmenu)",triggerEvent:"click"});});</script><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:10" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form>
<form id="resultListForm" name="resultListForm" method="post" action="/search/zh/result.jsf?_vid=P20-MDFS25-41499" class="resultlist-form" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="resultListForm" value="resultListForm" />
<div id="resultListForm:resultListPanel" class="ui-outputpanel ui-widget  js-display-text-language"><div id="resultListForm:j_idt2033" class="ui-outputpanel ui-widget"><div id="resultListForm:resultTable" class="ui-datatable ui-widget patent-result-list js-result-list"><div class="ui-datatable-tablewrapper"><table role="grid" class="b-table"><thead id="resultListForm:resultTable_head"><tr role="row"><th id="resultListForm:resultTable:j_idt2036" class="ui-state-default" role="columnheader" scope="col"><span class="ui-column-title"></span></th></tr></thead><tbody id="resultListForm:resultTable_data" class="ui-datatable-data ui-widget-content" tabindex="0"><tr data-ri="0" data-rk="**********" class="ui-widget-content ui-datatable-even ui-datatable-selectable ui-state-highlight trans-result-list-row" role="row" aria-selected="true"><td role="gridcell">
		<div id="resultListForm:resultTable:0:patentResult" class="ps-patent-result" data-mt-ipc="H04L 12/24">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">1.</span><a href="detail.jsf?docId=**********&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">102055605</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>一种应用于<span class="term-highlight">AAA</span>服务器的容灾系统及方法</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">CN</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:0:resultListTableColumnPubDate" class="notranslate">11.05.2011</span>
				</div>
			</div><div id="resultListForm:resultTable:0:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:0:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:0:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:0:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:0:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04L0012240000&amp;menulang=zh&amp;lang=zh" target="_blank">H04L 12/24
					</a>
				</span></span><span id="resultListForm:resultTable:0:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:0:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:0:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">L</td><td class="ipctxt">数字信息的传输,例如电报通信</td></td><tr class="ipc_GROUP"><td class="ipccod">12</td><td class="ipctxt">数据交换网络</td></td><tr class="ipc_elt"><td class="ipccod_interm">02</td><td class="ipctxt">零部件</td></td><tr class="ipc_elt"><td class="ipccod">24</td><td class="ipctxt">用于维护或管理的装置</td></td></table></div></div></div><script id="resultListForm:resultTable:0:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_0_j_idt2060",{id:"resultListForm:resultTable:0:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:0:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:0:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">200910210845.2
			</span>
			</span>
			<span id="resultListForm:resultTable:0:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ZTE Corporation
			</span>
			</span>
			<span id="resultListForm:resultTable:0:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">Zhou Junchao
			</span>
			</span></div><div id="resultListForm:resultTable:0:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span><p num="1">本发明公开了一种应用于<span class="term-highlight">AAA</span>服务器的容灾系统，包括<span class="term-highlight">AAA</span>服务器，以及与<span class="term-highlight">AAA</span>服务器连接的<span class="term-highlight">AAA</span>容灾服务器；所述<span class="term-highlight">AAA</span>容灾服务器与所述<span class="term-highlight">AAA</span>服务器配置相同；所述<span class="term-highlight">AAA</span>服务器正常运行时，所述<span class="term-highlight">AAA</span>容灾服务器用于接收并存储所述<span class="term-highlight">AAA</span>服务器发送的用户数据和运行配置数据的备份数据；所述<span class="term-highlight">AAA</span>服务器出现故障时，所述<span class="term-highlight">AAA</span>容灾服务器代替所述<span class="term-highlight">AAA</span>服务器，进行业务处理。本发明公开了一种应用于<span class="term-highlight">AAA</span>服务器的容灾方法。本发明在<span class="term-highlight">AAA</span>服务器双机中的两台主机同时出现问题或者磁阵设备出现故障的时候，由于<span class="term-highlight">AAA</span>服务器与<span class="term-highlight">AAA</span>容灾服务器配置相同，可以不依赖外部网元，自动切换有效的降低发生宕机等严重故障时所带来的影响。</p></span></div></div></div>
			
		</div></td></tr><tr data-ri="1" data-rk="WO2012152122" class="ui-widget-content ui-datatable-odd ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:1:patentResult" class="ps-patent-result" data-mt-ipc="H04W 8/26">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">2.</span><a href="detail.jsf?docId=WO2012152122&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">WO/2012/152122</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>实现虚拟<span class="term-highlight">AAA</span>服务器的方法及<span class="term-highlight">AAA</span>服务器</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">WO</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:1:resultListTableColumnPubDate" class="notranslate">15.11.2012</span>
				</div>
			</div><div id="resultListForm:resultTable:1:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:1:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:1:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:1:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:1:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04W0008260000&amp;menulang=zh&amp;lang=zh" target="_blank">H04W 8/26
					</a>
				</span></span><span id="resultListForm:resultTable:1:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:1:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:1:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">W</td><td class="ipctxt">无线通信网络</td></td><tr class="ipc_GROUP"><td class="ipccod">8</td><td class="ipctxt">网络数据管理</td></td><tr class="ipc_elt"><td class="ipccod">26</td><td class="ipctxt">用于移动性支持的网络寻址或编号</td></td></table></div></div></div><script id="resultListForm:resultTable:1:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_1_j_idt2060",{id:"resultListForm:resultTable:1:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:1:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:1:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">PCT/CN2012/072558
			</span>
			</span>
			<span id="resultListForm:resultTable:1:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ZTE CORPORATION
			</span>
			</span>
			<span id="resultListForm:resultTable:1:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">ZHOU, Junchao
			</span>
			</span></div><div id="resultListForm:resultTable:1:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span>本发明揭示了一种实现虚拟<span class="term-highlight">AAA</span>服务器的方法，包括：根据用户发起的业务请求，获取所述业务请求对应虚拟授权、验证、记账<span class="term-highlight">AAA</span>服务器的业务处理逻辑；所述虚拟<span class="term-highlight">AAA</span>服务器为物理<span class="term-highlight">AAA</span>服务器所预设的多个虚拟<span class="term-highlight">AAA</span>服务器的其中一个；根据所述业务处理逻辑，对业务进行处理。本发明还提出了对应的装置。本发明提供的一种实现虚拟<span class="term-highlight">AAA</span>服务器的方法及<span class="term-highlight">AAA</span>服务器，使得<span class="term-highlight">AAA</span>服务器具有可扩展性，以满足业务开展和客户需求。</span></div></div></div>
			
		</div></td></tr><tr data-ri="2" data-rk="WO2017166936" class="ui-widget-content ui-datatable-even ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:2:patentResult" class="ps-patent-result" data-mt-ipc="H04L 29/12">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">3.</span><a href="detail.jsf?docId=WO2017166936&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">WO/2017/166936</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>一种实现地址管理的方法、装置、<span class="term-highlight">AAA</span>服务器及SDN控制器</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">WO</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:2:resultListTableColumnPubDate" class="notranslate">05.10.2017</span>
				</div>
			</div><div id="resultListForm:resultTable:2:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:2:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:2:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:2:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:2:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04L0029120000&amp;menulang=zh&amp;lang=zh" target="_blank">H04L 29/12
					</a>
				</span></span><span id="resultListForm:resultTable:2:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:2:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:2:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">L</td><td class="ipctxt">数字信息的传输,例如电报通信</td></td><tr class="ipc_GROUP"><td class="ipccod">29</td><td class="ipctxt">Arrangements, apparatus, circuits or systems, not covered by a single one of groups H04L1/-H04L27/136</td></td><tr class="ipc_elt"><td class="ipccod">12</td><td class="ipctxt">以数据终端为特征的</td></td></table></div></div></div><script id="resultListForm:resultTable:2:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_2_j_idt2060",{id:"resultListForm:resultTable:2:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:2:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:2:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">PCT/CN2017/073747
			</span>
			</span>
			<span id="resultListForm:resultTable:2:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ZTE CORPORATION
			</span>
			</span>
			<span id="resultListForm:resultTable:2:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">WU, Bo
			</span>
			</span></div><div id="resultListForm:resultTable:2:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span>本文公布了一种实现地址管理的方法、装置、SDN控制器及<span class="term-highlight">AAA</span>服务器，包括：SDN控制器根据接收的来自第一装置的RG的识别和认证相关信息生成并发送进行家庭网关RG认证的认证请求信息到<span class="term-highlight">AAA</span>服务器；<span class="term-highlight">AAA</span>服务器在完成RG的认证后，为VG分配地址管理信息；SDN控制器在<span class="term-highlight">AAA</span>服务器完成RG认证后，根据用户签约信息为RG分配VG，SDN控制器根据来自<span class="term-highlight">AAA</span>服务器的携带有为VG分配的地址管理信息的内容的认证响应信息为VG配置地址。本发明实施例方法通过SDN控制器为RG分配VG，通过<span class="term-highlight">AAA</span>服务器为VG分配地址管理信息，实现了VG创建后的地址管理。</span></div></div></div>
			
		</div></td></tr><tr data-ri="3" data-rk="CN84758328" class="ui-widget-content ui-datatable-odd ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:3:patentResult" class="ps-patent-result" data-mt-ipc="H04W 8/26">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">4.</span><a href="detail.jsf?docId=CN84758328&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">102186165</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>实现虚拟<span class="term-highlight">AAA</span>服务器的方法及<span class="term-highlight">AAA</span>服务器</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">CN</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:3:resultListTableColumnPubDate" class="notranslate">14.09.2011</span>
				</div>
			</div><div id="resultListForm:resultTable:3:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:3:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:3:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:3:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:3:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04W0008260000&amp;menulang=zh&amp;lang=zh" target="_blank">H04W 8/26
					</a>
				</span></span><span id="resultListForm:resultTable:3:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:3:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:3:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">W</td><td class="ipctxt">无线通信网络</td></td><tr class="ipc_GROUP"><td class="ipccod">8</td><td class="ipctxt">网络数据管理</td></td><tr class="ipc_elt"><td class="ipccod">26</td><td class="ipctxt">用于移动性支持的网络寻址或编号</td></td></table></div></div></div><script id="resultListForm:resultTable:3:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_3_j_idt2060",{id:"resultListForm:resultTable:3:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:3:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:3:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">201110116494.6
			</span>
			</span>
			<span id="resultListForm:resultTable:3:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ZTE Corporation
			</span>
			</span>
			<span id="resultListForm:resultTable:3:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">Zhou Junchao
			</span>
			</span></div><div id="resultListForm:resultTable:3:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span><p num="1">本发明揭示了一种实现虚拟<span class="term-highlight">AAA</span>服务器的方法，包括：根据用户发起的业务请求，获取所述业务请求对应虚拟授权、验证、记账<span class="term-highlight">AAA</span>服务器的业务处理逻辑；所述虚拟<span class="term-highlight">AAA</span>服务器为物理<span class="term-highlight">AAA</span>服务器所预设的多个虚拟<span class="term-highlight">AAA</span>服务器的其中一个；根据所述业务处理逻辑，对业务进行处理。本发明还提出了对应的装置。本发明提供的一种实现虚拟<span class="term-highlight">AAA</span>服务器的方法及<span class="term-highlight">AAA</span>服务器，使得<span class="term-highlight">AAA</span>服务器具有可扩展性，以满足业务开展和客户需求。</p></span></div></div></div>
			
		</div></td></tr><tr data-ri="4" data-rk="KR598306" class="ui-widget-content ui-datatable-even ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:4:patentResult" class="ps-patent-result" data-mt-ipc="H04L 12/14">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">5.</span><a href="detail.jsf?docId=KR598306&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">1020040040709</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="en"><span class="trans-control"></span><span class="term-highlight">AAA</span> PROTOCOL-BASED BATCH PROCESS BILLING METHOD</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">KR</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:4:resultListTableColumnPubDate" class="notranslate">13.05.2004</span>
				</div>
			</div><div id="resultListForm:resultTable:4:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:4:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:4:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:4:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:4:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04L0012140000&amp;menulang=zh&amp;lang=zh" target="_blank">H04L 12/14
					</a>
				</span></span><span id="resultListForm:resultTable:4:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:4:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:4:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">L</td><td class="ipctxt">数字信息的传输,例如电报通信</td></td><tr class="ipc_GROUP"><td class="ipccod">12</td><td class="ipctxt">数据交换网络</td></td><tr class="ipc_elt"><td class="ipccod_interm">02</td><td class="ipctxt">零部件</td></td><tr class="ipc_elt"><td class="ipccod">14</td><td class="ipctxt">计费装置</td></td></table></div></div></div><script id="resultListForm:resultTable:4:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_4_j_idt2060",{id:"resultListForm:resultTable:4:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:4:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:4:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">1020020068908
			</span>
			</span>
			<span id="resultListForm:resultTable:4:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ELECTRONICS AND TELECOMMUNICATIONS RESEARCH INSTITUTE
			</span>
			</span>
			<span id="resultListForm:resultTable:4:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">KIM, HYEON GON
			</span>
			</span></div><div id="resultListForm:resultTable:4:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="en"><span class="trans-control"></span><P>PURPOSE: An <span class="term-highlight">AAA</span>(Authentication,Authorization,Accounting) protocol-based batch process billing method is provided to batch-process billing data which has been failed for transmission during a real time billing processing.</P><P>CONSTITUTION: An <span class="term-highlight">AAA</span> client requests user authentication from an <span class="term-highlight">AAA</span> server(S301). Upon completion of user authentication, the <span class="term-highlight">AAA</span> server generates related data on an authentication success or failure and transmits it to the <span class="term-highlight">AAA</span> client(S302). The <span class="term-highlight">AAA</span> client informs the user whether access to a user-requested resource is allowed or rejected. If allowed, the user can receive a service, and in this case, the <span class="term-highlight">AAA</span> client generates billing data, generates a billing data transmission request(ACR) and transmits it to the <span class="term-highlight">AAA</span> server(S303). The <span class="term-highlight">AAA</span> server checks the ACR message and stores it(S304). If the <span class="term-highlight">AAA</span> server successfully transmits corresponding billing data to the <span class="term-highlight">AAA</span> client(S305), billing data transmission success is confirmed(S307). If billing data transmission is failed(S306), billing data transmission failure is confirmed(S308). In the case of the billing data transmission failure, the <span class="term-highlight">AAA</span> client sets an ID for batch-billing application, generates a billing request message(ACR) and transmits it to the <span class="term-highlight">AAA</span> server(S309). Then, the <span class="term-highlight">AAA</span> server updates a corresponding session(S310). The <span class="term-highlight">AAA</span> server generates a billing response message(ACA) and transmits it together with a process result to the <span class="term-highlight">AAA</span> client(S311).</P><P>© KIPO 2004</P></span></div></div></div>
			
		</div></td></tr><tr data-ri="5" data-rk="CN107121742" class="ui-widget-content ui-datatable-odd ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:5:patentResult" class="ps-patent-result" data-mt-ipc="G01N 33/53">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">6.</span><a href="detail.jsf?docId=CN107121742&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">103988080</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>用于腹主动脉瘤的生物标记物</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">CN</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:5:resultListTableColumnPubDate" class="notranslate">13.08.2014</span>
				</div>
			</div><div id="resultListForm:resultTable:5:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:5:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:5:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:5:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:5:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=G01N0033530000&amp;menulang=zh&amp;lang=zh" target="_blank">G01N 33/53
					</a>
				</span></span><span id="resultListForm:resultTable:5:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:5:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:5:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">G</td><td class="ipctxt">物理</td></td><tr class="ipc_CLASS"><td class="ipccod">01</td><td class="ipctxt">用于测量的传感器,如敏感元件</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">N</td><td class="ipctxt">借助于测定材料的化学或物理性质来测试或分析材料</td></td><tr class="ipc_GROUP"><td class="ipccod">33</td><td class="ipctxt">利用不包括在G01N1/00至G01N31/00组中的特殊方法来研究或分析材料</td></td><tr class="ipc_elt"><td class="ipccod_interm">48</td><td class="ipctxt">生物物质,例如血、尿;血球计数器</td></td><tr class="ipc_elt"><td class="ipccod_interm">50</td><td class="ipctxt">生物物质(例如血、尿)的化学分析;包括了生物特有的配体结合方法的测试;免疫学试验</td></td><tr class="ipc_elt"><td class="ipccod">53</td><td class="ipctxt">免疫测定法;生物特异性结合测定;相应的生物物质</td></td></table></div></div></div><script id="resultListForm:resultTable:5:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_5_j_idt2060",{id:"resultListForm:resultTable:5:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:5:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:5:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">201280060568.1
			</span>
			</span>
			<span id="resultListForm:resultTable:5:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">加利福尼亚大学董事会
			</span>
			</span>
			<span id="resultListForm:resultTable:5:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">才华
			</span>
			</span></div><div id="resultListForm:resultTable:5:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span><p num="1">本发明涉及检测个体的腹主动脉瘤<span class="term-highlight">AAA</span>或对<span class="term-highlight">AAA</span>的易感性的方法、监测个体的<span class="term-highlight">AAA</span>的治疗功效的方法和评估个体的<span class="term-highlight">AAA</span>的严重程度或<span class="term-highlight">AAA</span>的风险的方法，所述方法涉及测量测试样品中存在的四氢生物喋呤H4B的量并且将其与标准物或先前测试样品中存在的H4B的量进行比较。所述测试样品中存在的H4B的量与所述标准物相比降低指示<span class="term-highlight">AAA</span>或对<span class="term-highlight">AAA</span>的易感性。可在第二时间点之前向所述个体投与治疗，且第二测试样品中存在的H4B的量与所述第一测试样品相比增加指示<span class="term-highlight">AAA</span>的有效治疗。可鉴别出候选者用于进一步测试或监测<span class="term-highlight">AAA</span>和/或治疗<span class="term-highlight">AAA</span>。</p></span></div></div></div>
			
		</div></td></tr><tr data-ri="6" data-rk="WO2013139217" class="ui-widget-content ui-datatable-even ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:6:patentResult" class="ps-patent-result" data-mt-ipc="H04W 28/16">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">7.</span><a href="detail.jsf?docId=WO2013139217&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">WO/2013/139217</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>一种接口建立方法及装置</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">WO</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:6:resultListTableColumnPubDate" class="notranslate">26.09.2013</span>
				</div>
			</div><div id="resultListForm:resultTable:6:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:6:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:6:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:6:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:6:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04W0028160000&amp;menulang=zh&amp;lang=zh" target="_blank">H04W 28/16
					</a>
				</span></span><span id="resultListForm:resultTable:6:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:6:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:6:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">W</td><td class="ipctxt">无线通信网络</td></td><tr class="ipc_GROUP"><td class="ipccod">28</td><td class="ipctxt">网络业务量管理;网络资源管理</td></td><tr class="ipc_elt"><td class="ipccod">16</td><td class="ipctxt">中央资源管理;协商资源或通信参数,例如协商带宽或QoS</td></td></table></div></div></div><script id="resultListForm:resultTable:6:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_6_j_idt2060",{id:"resultListForm:resultTable:6:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:6:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:6:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">PCT/CN2013/072491
			</span>
			</span>
			<span id="resultListForm:resultTable:6:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">CHINA ACADEMY OF TELECOMMUNICATIONS TECHNOLOGY
			</span>
			</span>
			<span id="resultListForm:resultTable:6:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">DENG, Qiang
			</span>
			</span></div><div id="resultListForm:resultTable:6:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span>本发明公开了一种接口建立方法及装置，用以实现通过宽带网络网关（BNG）建立宽带论坛认证、授权和计费代理（BBF <span class="term-highlight">AAA</span> Proxy）与第三代合作项目认证、授权和计费服务器（3GPP <span class="term-highlight">AAA</span> Server）之间的接口。该方法包括：BNG获取建立BBF <span class="term-highlight">AAA</span> Proxy与3GPP <span class="term-highlight">AAA</span> Server之间的接口的指示信息（S101）；BNG根据所述指示信息，判断是否需要建立BBF <span class="term-highlight">AAA</span> Proxy与3GPP <span class="term-highlight">AAA</span> Server之间的接口（S102）；当需要建立BBF <span class="term-highlight">AAA</span> Proxy与3GPP <span class="term-highlight">AAA</span> Server之间的接口时，BNG发起BBF <span class="term-highlight">AAA</span> Proxy与3GPP <span class="term-highlight">AAA</span> Server之间的接口的建立过程（S103）。</span></div></div></div>
			
		</div></td></tr><tr data-ri="7" data-rk="WO2012163159" class="ui-widget-content ui-datatable-odd ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:7:patentResult" class="ps-patent-result" data-mt-ipc="H04L 29/06">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">8.</span><a href="detail.jsf?docId=WO2012163159&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">WO/2012/163159</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="zh"><span class="trans-control"></span>实现企业网<span class="term-highlight">AAA</span>服务器与公网<span class="term-highlight">AAA</span>服务器合一的方法及装置</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">WO</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:7:resultListTableColumnPubDate" class="notranslate">06.12.2012</span>
				</div>
			</div><div id="resultListForm:resultTable:7:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:7:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:7:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:7:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:7:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=H04L0029060000&amp;menulang=zh&amp;lang=zh" target="_blank">H04L 29/06
					</a>
				</span></span><span id="resultListForm:resultTable:7:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:7:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:7:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">H</td><td class="ipctxt">电学</td></td><tr class="ipc_CLASS"><td class="ipccod">04</td><td class="ipctxt">电通信技术</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">L</td><td class="ipctxt">数字信息的传输,例如电报通信</td></td><tr class="ipc_GROUP"><td class="ipccod">29</td><td class="ipctxt">Arrangements, apparatus, circuits or systems, not covered by a single one of groups H04L1/-H04L27/136</td></td><tr class="ipc_elt"><td class="ipccod_interm">02</td><td class="ipctxt">通信控制;通信处理</td></td><tr class="ipc_elt"><td class="ipccod">06</td><td class="ipctxt">以协议为特征的</td></td></table></div></div></div><script id="resultListForm:resultTable:7:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_7_j_idt2060",{id:"resultListForm:resultTable:7:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:7:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:7:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">PCT/CN2012/073066
			</span>
			</span>
			<span id="resultListForm:resultTable:7:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ZTE CORPORATION
			</span>
			</span>
			<span id="resultListForm:resultTable:7:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">ZHOU, Junchao
			</span>
			</span></div><div id="resultListForm:resultTable:7:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="zh"><span class="trans-control"></span>本发明公开了一种实现企业网<span class="term-highlight">AAA</span>服务器与公网<span class="term-highlight">AAA</span>服务器合一的方法及装置，所述方法包括：接收用户发起的认证请求；所述认证请求包括企业的ISP域名；认证所述用户作为公网用户和企业网用户的合法性；所述企业网为所述ISP域名对应的企业网；认证通过时，为所述用户提供分组业务处理。本发明通过提供一种实现企业网<span class="term-highlight">AAA</span>服务器与公网<span class="term-highlight">AAA</span>服务器合一的方法及装置，节约企业成本的同时简化了认证流程。</span></div></div></div>
			
		</div></td></tr><tr data-ri="8" data-rk="EP307384270" class="ui-widget-content ui-datatable-even ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:8:patentResult" class="ps-patent-result" data-mt-ipc="G06F 15/16">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">9.</span><a href="detail.jsf?docId=EP307384270&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">3718016</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="de"><span class="trans-control"></span>VERFAHREN ZUR MIGRATION EINER SITZUNGSABRECHNUNG ZU EINEM ZUSTANDSBEHAFTETEN ABRECHNUNGSPEER</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">EP</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:8:resultListTableColumnPubDate" class="notranslate">07.10.2020</span>
				</div>
			</div><div id="resultListForm:resultTable:8:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:8:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:8:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:8:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:8:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=G06F0015160000&amp;menulang=zh&amp;lang=zh" target="_blank">G06F 15/16
					</a>
				</span></span><span id="resultListForm:resultTable:8:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:8:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:8:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">G</td><td class="ipctxt">物理</td></td><tr class="ipc_CLASS"><td class="ipccod">06</td><td class="ipctxt">计算;推算或计数</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">F</td><td class="ipctxt">电数字数据处理</td></td><tr class="ipc_GROUP"><td class="ipccod">15</td><td class="ipctxt">通用数字计算机;通用数据处理设备</td></td><tr class="ipc_elt"><td class="ipccod">16</td><td class="ipctxt">两个或多个数字计算机的组合,每台计算机至少具有一个运算单元、一个程序单元和一个寄存器,例如,用于数个程序的同时处理</td></td></table></div></div></div><script id="resultListForm:resultTable:8:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_8_j_idt2060",{id:"resultListForm:resultTable:8:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:8:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:8:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">17933194
			</span>
			</span>
			<span id="resultListForm:resultTable:8:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">ERICSSON TELEFON AB L M
			</span>
			</span>
			<span id="resultListForm:resultTable:8:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">VENKATARAMAN NATARAJAN
			</span>
			</span></div><div id="resultListForm:resultTable:8:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="en"><span class="trans-control"></span>A method for migrating a subscriber session from a first authentication, authorization and accounting (<span class="term-highlight">AAA</span>) accounting peer to a second <span class="term-highlight">AAA</span> accounting peer, where the first <span class="term-highlight">AAA</span> accounting peer is stateful. The method includes receiving an accounting start packet from an <span class="term-highlight">AAA</span> client application, forwarding the accounting start packet to the first <span class="term-highlight">AAA</span> accounting peer, receiving an accounting update or accounting stop packet from the <span class="term-highlight">AAA</span> client application; and sending an accounting start packet from the <span class="term-highlight">AAA</span> client application to the second <span class="term-highlight">AAA</span> accounting peer, in response to a connection failure with the first <span class="term-highlight">AAA</span> accounting peer.</span></div></div></div>
			
		</div></td></tr><tr data-ri="9" data-rk="WO2019106681" class="ui-widget-content ui-datatable-odd ui-datatable-selectable trans-result-list-row" role="row" aria-selected="false"><td role="gridcell">
		<div id="resultListForm:resultTable:9:patentResult" class="ps-patent-result" data-mt-ipc="G06F 15/16">
			
			<div class="ps-patent-result--first-row">
				
				<div class="ps-patent-result--title">
					<span class="notranslate ps-patent-result--title--record-number">10.</span><a href="detail.jsf?docId=WO2019106681&amp;_cid=P20-MDFS25-41499-1" onclick="" target="_self"><span class="notranslate ps-patent-result--title--patent-number">WO/2019/106681</span></a><span class="ps-patent-result--title--title content--text-wrap"><span class="trans-section needTranslation-title" lang="en"><span class="trans-control"></span>METHOD FOR MIGRATION OF SESSION ACCOUNTING TO A DIFFERENT STATEFUL ACCOUNTING PEER</span></span>
				</div>

				<div class="ps-patent-result--title--ctr-pubdate"><span class="notranslate">WO</span>
					<span class="notranslate">- </span><span id="resultListForm:resultTable:9:resultListTableColumnPubDate" class="notranslate">06.06.2019</span>
				</div>
			</div><div id="resultListForm:resultTable:9:j_idt2048" class="ui-outputpanel ui-widget ps-patent-result--second-row"><div id="resultListForm:resultTable:9:j_idt2049" class="ui-outputpanel ui-widget ps-patent-result--fields"><div id="resultListForm:resultTable:9:j_idt2050" class="ui-outputpanel ui-widget ps-patent-result--fields--group">
			<span id="resultListForm:resultTable:9:j_idt2051" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					国际分类
				
			</span>
			
			<span class="ps-field--value ps-patent-result--ipc notranslate"><span id="resultListForm:resultTable:9:PCTipc">
				<span>
					<a href="https://www.wipo.int/ipcpub/?symbol=G06F0015160000&amp;menulang=zh&amp;lang=zh" target="_blank">G06F 15/16
					</a>
				</span></span><span id="resultListForm:resultTable:9:singleIpcTooltip" class="js-ipc-tooltip-help"><span id="resultListForm:resultTable:9:IPCHelpIcon" class="icon-wrapper icon-wrapper--is-16 question-icon"></span><div id="resultListForm:resultTable:9:j_idt2060" class="ui-tooltip ui-widget notranslate ps-tooltip ipc-tooltip ui-tooltip-right"><div class="ui-tooltip-arrow"></div><div class="ui-tooltip-text ui-shadow ui-corner-all"><div class="patent-classification"><table class="ipc"><tr class="ipc_MAINCLASS"><td class="ipccod">G</td><td class="ipctxt">物理</td></td><tr class="ipc_CLASS"><td class="ipccod">06</td><td class="ipctxt">计算;推算或计数</td></td><tr class="ipc_SUBCLASS"><td class="ipccod">F</td><td class="ipctxt">电数字数据处理</td></td><tr class="ipc_GROUP"><td class="ipccod">15</td><td class="ipctxt">通用数字计算机;通用数据处理设备</td></td><tr class="ipc_elt"><td class="ipccod">16</td><td class="ipctxt">两个或多个数字计算机的组合,每台计算机至少具有一个运算单元、一个程序单元和一个寄存器,例如,用于数个程序的同时处理</td></td></table></div></div></div><script id="resultListForm:resultTable:9:j_idt2060_s" type="text/javascript">$(function(){PrimeFaces.cw("Tooltip","widget_resultListForm_resultTable_9_j_idt2060",{id:"resultListForm:resultTable:9:j_idt2060",showEffect:"fade",hideEffect:"fade",target:"resultListForm:resultTable:9:IPCHelpIcon"});});</script></span>
			</span>
			</span>
			<span id="resultListForm:resultTable:9:j_idt2089" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请号
				
			</span>
			
			<span class="ps-field--value notranslate">PCT/IN2017/050566
			</span>
			</span>
			<span id="resultListForm:resultTable:9:j_idt2111" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					申请人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--applicant notranslate">TELEFONAKTIEBOLAGET LM ERICSSON (PUBL)
			</span>
			</span>
			<span id="resultListForm:resultTable:9:j_idt2133" class="ps-field ps-field--is-layout--inline ">
			<span class="ps-field--label notranslate">
					发明人
				
			</span>
			
			<span class="ps-field--value ps-patent-result--inventor notranslate">VENKATARAMAN, Natarajan
			</span>
			</span></div><div id="resultListForm:resultTable:9:j_idt2200" class="ui-outputpanel ui-widget ps-patent-result--abstract"><span class="trans-section needTranslation-biblio" lang="en"><span class="trans-control"></span>A method for migrating a subscriber session from a first authentication, authorization and accounting (<span class="term-highlight">AAA</span>) accounting peer to a second <span class="term-highlight">AAA</span> accounting peer, where the first <span class="term-highlight">AAA</span> accounting peer is stateful. The method includes receiving an accounting start packet from an <span class="term-highlight">AAA</span> client application, forwarding the accounting start packet to the first <span class="term-highlight">AAA</span> accounting peer, receiving an accounting update or accounting stop packet from the <span class="term-highlight">AAA</span> client application; and sending an accounting start packet from the <span class="term-highlight">AAA</span> client application to the second <span class="term-highlight">AAA</span> accounting peer, in response to a connection failure with the first <span class="term-highlight">AAA</span> accounting peer.</span></div></div></div>
			
		</div></td></tr></tbody></table></div><input type="hidden" id="resultListForm:resultTable_selection" name="resultListForm:resultTable_selection" autocomplete="off" value="**********" /></div><script id="resultListForm:resultTable_s" type="text/javascript">$(function(){PrimeFaces.cw("DataTable","w_resultTable",{id:"resultListForm:resultTable",selectionMode:"single",groupColumnIndexes:[],behaviors:{rowSelect:function(ext,event) {}}});});</script></div><div id="resultListForm:j_idt2238" class="ui-outputpanel ui-widget ps-paginator-bottom">
		<div class="ps-paginator"><span id="resultListForm:j_idt2242" class="ui-commandlink ui-widget ui-state-disabled ps-link--has-icon js-paginator-prev">
				<span class="icon-wrapper chevron-left-icon"></span></span>
			
			
			<div class="ps-paginator--page"><a id="resultListForm:invalidPageNumber" href="#" class="ui-commandlink ui-widget ps-paginator--page--select" aria-label="单击以转至特定页面" onclick="PF('w_paginatorChangePage').show('resultListForm:invalidPageNumber');return false;;PrimeFaces.ab({s:&quot;resultListForm:invalidPageNumber&quot;});return false;" title="单击以转至特定页面">
					<span class="ps-paginator--page--value"><div id="resultListForm:j_idt2246" aria-live="polite" class="ps-paginator--page--error ui-message"></div><span id="resultListForm:pageNumber">1</span> / 564
						<span class="ps-paginator--page--icon icon-wrapper icon-wrapper--is-16 small-arrow-down-icon"></span>
					</span></a>
			</div><a id="resultListForm:j_idt2251" href="#" class="ui-commandlink ui-widget ps-link--has-icon js-paginator-next" aria-label="下一页" onclick="PrimeFaces.ab({s:&quot;resultListForm:j_idt2251&quot;,p:&quot;resultListForm:j_idt2251&quot;,u:&quot;results-container @(.js-ps-global-messages)&quot;,onco:function(xhr,status,args){createCustomEvent('paginator.changePage');;}});return false;" title="下一页">
				<span class="icon-wrapper chevron-right-icon"></span></a>
		</div></div></div><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:11" value="5361595396123951474:5803823086469651427" autocomplete="off" />
</form></div></div>
		    	</div>
		    	
		    	<div class="b-step__content-bottom">
		    	</div>
			 	
			 </div>
</div>
	
	
	
	
	<div class="c-footer"> # - 
	</div>
</div><script type="text/javascript" id="j_idt30">
		if(typeof(load_w_scripts) != 'undefined') load_w_scripts();
	</script><script type="text/javascript" id="init_w_ps_components">
			load_w_ps_cc_scripts();
		</script><script type="text/javascript">OmniFaces.DeferredScript.add('/search/javax.faces.resource/js/components.js.jsf?ln=ps-cc');</script><script type="text/javascript">OmniFaces.DeferredScript.add('/search/javax.faces.resource/js/wide-layout/result-hotkeys.js.jsf');</script></body>
</html>