# NICE商标分类数据爬虫

这是一个用于从WIPO官网获取NICE商标分类数据并清洗入库的工具。

## 功能特性

- 🚀 支持爬取1-45类完整的NICE商标分类数据
- 🔧 灵活的数据库配置方式（配置文件/环境变量）
- 📊 数据清洗和格式化
- 💾 批量入库，支持数据更新
- 📈 详细的日志记录和统计信息
- ⚡ 支持指定分类范围爬取

## 数据源

- **官方网站**: https://nclpub.wipo.int/enfr/
- **数据版本**: 2025.12
- **分类范围**: 1-45类
- **语言**: 英文

## 安装依赖

```bash
pip install requests beautifulsoup4 pymysql
```

## 配置数据库

### 方式1: 配置文件（推荐）

1. 复制示例配置文件：
```bash
cp db_config.json.example db_config.json
```

2. 修改 `db_config.json` 中的数据库连接信息：
```json
{
  "host": "localhost",
  "port": 3306,
  "user": "root",
  "password": "your_password",
  "database": "db_trademark_staging"
}
```

### 方式2: 环境变量

```bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_DATABASE=db_trademark_staging
```

## 使用方法

### 创建配置文件
```bash
python nice_spider.py --create-config
```

### 爬取全部分类数据
```bash
python nice_spider.py
```

### 爬取指定分类
```bash
# 爬取第1、2、3类
python nice_spider.py --classes 1 2 3
```

### 清理现有数据后重新爬取
```bash
python nice_spider.py --clear
```

### 查看数据库统计信息
```bash
python nice_spider.py --stats
```

### 指定配置文件
```bash
python nice_spider.py --config /path/to/your/config.json
```

## 数据库表结构

工具会将数据保存到 `trademark_class_map` 表中，表结构如下：

```sql
CREATE TABLE trademark_class_map (
    id                   BIGINT AUTO_INCREMENT PRIMARY KEY,
    class_type           VARCHAR(10) DEFAULT '' NOT NULL COMMENT '商标分类类型',
    class_id             VARCHAR(30) DEFAULT '' NOT NULL COMMENT '商标分类ID',
    class_level_1        VARCHAR(10) DEFAULT '' NOT NULL COMMENT '分类层级1',
    class_description_en LONGTEXT               NOT NULL COMMENT '分类描述(en)',
    class_description_cn LONGTEXT               NOT NULL COMMENT '分类描述(cn)',
    version              VARCHAR(10) DEFAULT '' NOT NULL COMMENT '分类版本',
    create_time          DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建日期',
    update_time          DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    UNIQUE KEY uni_class_id (class_id, version)
) COMMENT '商标分类(NICE/VIENNA)配置表';
```

## 数据示例

| class_type | class_id | class_level_1 | class_description_en | version |
|------------|----------|---------------|---------------------|---------|
| NICE | 010008 | 1 | acetate of cellulose, unprocessed | 2025.12 |
| NICE | 010007 | 1 | acetates [chemicals] | 2025.12 |
| NICE | 020006 | 2 | alizarine dyes | 2025.12 |

## 日志文件

运行过程中会生成 `nice_spider.log` 日志文件，记录详细的运行信息。

## 注意事项

1. 请确保数据库表已创建
2. 建议在非高峰期运行，避免对官网造成压力
3. 工具内置了请求延迟，请勿修改
4. 如遇网络问题，可重新运行，工具支持断点续传

## 故障排除

### 数据库连接失败
- 检查数据库配置信息是否正确
- 确认数据库服务是否启动
- 检查网络连接

### 爬取失败
- 检查网络连接
- 确认目标网站是否可访问
- 查看日志文件获取详细错误信息

### 数据解析异常
- 可能是网站结构发生变化
- 查看日志文件中的详细错误信息
- 检查HTML内容是否正常

## 开发者信息

- 支持的Python版本: 3.7+
- 依赖库: requests, beautifulsoup4, pymysql
- 编码: UTF-8
