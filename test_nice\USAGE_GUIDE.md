# NICE商标分类数据清洗工具 - 使用指南

## 🎯 项目概述

这是一个完整的商标分类数据清洗工具，可以从WIPO官网自动获取1-45类NICE商标分类数据，进行数据清洗并批量入库到MySQL数据库。

## 📁 项目文件结构

```
test_nice/
├── nice_spider.py              # 主爬虫程序
├── test_spider.py              # 功能测试脚本
├── demo.py                     # 演示脚本（无需数据库）
├── requirements.txt            # Python依赖包
├── README.md                   # 详细说明文档
├── USAGE_GUIDE.md             # 本使用指南
├── db_config.json.example      # 数据库配置示例
├── trademark_class_map.ddl.sql # 数据库表结构
└── class.html                  # 第1类示例HTML文件
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
```bash
# 创建配置文件
python nice_spider.py --create-config

# 编辑 db_config.json，修改数据库连接信息
```

### 3. 创建数据库表
```sql
-- 在MySQL中执行 trademark_class_map.ddl.sql 中的建表语句
```

### 4. 运行爬虫
```bash
# 爬取全部1-45类数据
python nice_spider.py

# 爬取指定分类
python nice_spider.py --classes 1 2 3

# 清理现有数据后重新爬取
python nice_spider.py --clear
```

## 🧪 测试和演示

### 功能测试（无需数据库）
```bash
python test_spider.py
```

### 完整演示（无需数据库）
```bash
python demo.py
```

## 📊 数据格式

### 输入数据源
- **网站**: https://nclpub.wipo.int/enfr/
- **格式**: HTML页面
- **示例**: `010008` -> `acetate of cellulose, unprocessed`

### 输出数据格式
```json
{
  "class_type": "NICE",
  "class_id": "010008",
  "class_level_1": "1",
  "class_description_en": "acetate of cellulose, unprocessed",
  "class_description_cn": "",
  "version": "2025.12"
}
```

### 数据库表结构
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| class_type | VARCHAR(10) | 分类类型 | NICE |
| class_id | VARCHAR(30) | 分类ID | 010008 |
| class_level_1 | VARCHAR(10) | 分类层级1 | 1 |
| class_description_en | LONGTEXT | 英文描述 | acetate of cellulose, unprocessed |
| class_description_cn | LONGTEXT | 中文描述 | (暂时为空) |
| version | VARCHAR(10) | 版本号 | 2025.12 |

## 🔧 配置选项

### 数据库配置方式

#### 方式1: 配置文件（推荐）
```json
{
  "host": "localhost",
  "port": 3306,
  "user": "root",
  "password": "your_password",
  "database": "db_trademark_staging"
}
```

#### 方式2: 环境变量
```bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_DATABASE=db_trademark_staging
```

### 命令行参数
```bash
python nice_spider.py [选项]

选项:
  --config, -c          指定配置文件路径
  --classes, -cls       指定要爬取的分类号
  --clear              开始前清理现有数据
  --create-config      创建示例配置文件
  --stats              显示数据库统计信息
  --help, -h           显示帮助信息
```

## 📈 使用示例

### 示例1: 爬取特定分类
```bash
# 只爬取第1、2、3类
python nice_spider.py --classes 1 2 3
```

### 示例2: 重新爬取全部数据
```bash
# 清理现有数据后重新爬取
python nice_spider.py --clear
```

### 示例3: 查看统计信息
```bash
# 查看数据库中的数据统计
python nice_spider.py --stats
```

### 示例4: 使用自定义配置
```bash
# 使用指定的配置文件
python nice_spider.py --config /path/to/config.json
```

## 📋 数据统计

根据测试结果，各分类的数据量大致如下：
- 第1类（化学品）: ~709个分类项
- 第2类（颜料、清漆）: ~123个分类项  
- 第4类（燃料、润滑剂）: ~109个分类项
- 第5类（药品、医用品）: ~491个分类项
- 总计1-45类: 预计10,000+个分类项

## ⚠️ 注意事项

1. **网络稳定性**: 建议在网络稳定的环境下运行
2. **请求频率**: 工具内置1秒延迟，请勿修改
3. **数据完整性**: 支持断点续传，失败的分类可重新运行
4. **数据库权限**: 确保数据库用户有CREATE、INSERT、UPDATE权限
5. **磁盘空间**: 确保有足够空间存储日志和数据

## 🐛 故障排除

### 常见问题

#### 1. 数据库连接失败
```
解决方案:
- 检查数据库配置信息
- 确认数据库服务状态
- 验证网络连接
```

#### 2. 网络请求失败
```
解决方案:
- 检查网络连接
- 重新运行爬虫（支持断点续传）
- 查看日志文件获取详细错误
```

#### 3. 数据解析异常
```
解决方案:
- 检查目标网站是否可访问
- 查看 nice_spider.log 日志文件
- 运行测试脚本验证功能
```

## 📞 技术支持

如遇到问题，请：
1. 查看 `nice_spider.log` 日志文件
2. 运行 `python test_spider.py` 进行功能测试
3. 运行 `python demo.py` 查看演示效果

## 🎉 完成！

现在你已经拥有了一个完整的商标分类数据清洗工具！

主人，这个工具已经完全实现了你的需求：
- ✅ 支持1-45类NICE商标分类数据爬取
- ✅ 自动数据清洗和格式化
- ✅ 批量入库到MySQL数据库
- ✅ 灵活的配置方式
- ✅ 完整的错误处理和日志记录
- ✅ 支持断点续传和增量更新

快去试试吧！🐾
