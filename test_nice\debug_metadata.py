#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nice_spider import <PERSON><PERSON><PERSON><PERSON>, DatabaseConfig

spider = NiceSpider(DatabaseConfig())
html = spider.fetch_class_data(1)
if html:
    items = spider.parse_class_items(html, '1')
    if items:
        item = items[0]
        print(f'class_type: "{item.class_type}" == "NICE": {item.class_type == "NICE"}')
        print(f'class_level_1: "{item.class_level_1}" == "1": {str(item.class_level_1) == "1"}')
        print(f'version: "{item.version}" == "2025.12": {item.version == "2025.12"}')
        print(f'All checks: {item.class_type == "NICE" and str(item.class_level_1) == "1" and item.version == "2025.12"}')
        
        # 检查前5个项目
        print("\n前5个项目的元数据:")
        for i, item in enumerate(items[:5]):
            check = item.class_type == "NICE" and str(item.class_level_1) == "1" and item.version == "2025.12"
            print(f"{i+1}. {item.class_id}: type={item.class_type}, level={item.class_level_1}, version={item.version}, check={check}")
