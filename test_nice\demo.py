#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NICE商标分类爬虫演示脚本
展示如何使用爬虫获取和处理数据（无需数据库连接）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nice_spider import NiceSpider, DatabaseConfig
import json


def demo_fetch_and_parse():
    """演示数据获取和解析功能"""
    print("🚀 NICE商标分类数据爬虫演示")
    print("=" * 60)
    
    # 创建爬虫实例（不需要真实数据库配置）
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    print("📡 正在获取第2类（颜料、清漆、漆类）数据...")
    
    # 获取第2类数据
    html_content = spider.fetch_class_data(2)
    
    if not html_content:
        print("❌ 数据获取失败")
        return
    
    print(f"✅ 数据获取成功，内容长度: {len(html_content):,} 字符")
    
    # 解析数据
    print("🔍 正在解析数据...")
    items = spider.parse_class_items(html_content, "2")
    
    if not items:
        print("❌ 数据解析失败")
        return
    
    print(f"✅ 解析成功，共找到 {len(items)} 个分类项")
    
    # 显示前10个分类项
    print("\n📋 前10个分类项:")
    print("-" * 80)
    for i, item in enumerate(items[:10], 1):
        print(f"{i:2d}. {item.class_id} | {item.class_description_en}")
    
    # 显示统计信息
    print(f"\n📊 统计信息:")
    print(f"   • 分类类型: {items[0].class_type}")
    print(f"   • 分类层级: {items[0].class_level_1}")
    print(f"   • 数据版本: {items[0].version}")
    print(f"   • 总分类数: {len(items)}")
    
    # 描述长度统计
    desc_lengths = [len(item.class_description_en) for item in items]
    print(f"   • 描述长度: 最短 {min(desc_lengths)} 字符，最长 {max(desc_lengths)} 字符")
    
    # 保存示例数据到JSON文件
    sample_data = []
    for item in items[:20]:  # 保存前20个作为示例
        sample_data.append({
            'class_type': item.class_type,
            'class_id': item.class_id,
            'class_level_1': item.class_level_1,
            'class_description_en': item.class_description_en,
            'version': item.version
        })
    
    with open('sample_class2_data.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 已保存前20个分类项到 sample_class2_data.json")
    
    return items


def demo_multiple_classes():
    """演示批量获取多个分类的数据"""
    print("\n" + "=" * 60)
    print("🔄 批量获取多个分类演示")
    print("=" * 60)
    
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 获取第3、4、5类的数据
    target_classes = [3, 4, 5]
    all_results = {}
    
    for class_num in target_classes:
        print(f"\n📡 正在获取第{class_num}类数据...")
        
        html_content = spider.fetch_class_data(class_num)
        if html_content:
            items = spider.parse_class_items(html_content, str(class_num))
            all_results[class_num] = items
            print(f"✅ 第{class_num}类: {len(items)} 个分类项")
        else:
            print(f"❌ 第{class_num}类获取失败")
    
    # 汇总统计
    print(f"\n📊 批量获取汇总:")
    total_items = 0
    for class_num, items in all_results.items():
        total_items += len(items)
        print(f"   • 第{class_num}类: {len(items)} 个分类项")
    
    print(f"   • 总计: {total_items} 个分类项")
    
    # 保存汇总数据
    summary_data = {}
    for class_num, items in all_results.items():
        summary_data[f"class_{class_num}"] = {
            'count': len(items),
            'samples': [
                {
                    'class_id': item.class_id,
                    'description': item.class_description_en
                }
                for item in items[:3]  # 每类保存前3个作为示例
            ]
        }
    
    with open('multiple_classes_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, indent=2, ensure_ascii=False)
    
    print(f"💾 已保存汇总数据到 multiple_classes_summary.json")


def demo_data_analysis():
    """演示数据分析功能"""
    print("\n" + "=" * 60)
    print("📈 数据分析演示")
    print("=" * 60)
    
    # 读取之前保存的示例数据
    try:
        with open('sample_class2_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📂 读取示例数据: {len(data)} 条记录")
        
        # 分析描述长度分布
        lengths = [len(item['class_description_en']) for item in data]
        
        print(f"\n📏 描述长度分析:")
        print(f"   • 平均长度: {sum(lengths) / len(lengths):.1f} 字符")
        print(f"   • 最短描述: {min(lengths)} 字符")
        print(f"   • 最长描述: {max(lengths)} 字符")
        
        # 找出最短和最长的描述
        shortest = min(data, key=lambda x: len(x['class_description_en']))
        longest = max(data, key=lambda x: len(x['class_description_en']))
        
        print(f"\n🔍 极值示例:")
        print(f"   • 最短: {shortest['class_id']} - {shortest['class_description_en']}")
        print(f"   • 最长: {longest['class_id']} - {longest['class_description_en']}")
        
        # 分析包含特定关键词的分类
        keywords = ['paint', 'color', 'dye', 'ink']
        print(f"\n🔎 关键词分析:")
        for keyword in keywords:
            count = sum(1 for item in data if keyword.lower() in item['class_description_en'].lower())
            print(f"   • 包含 '{keyword}': {count} 个分类")
        
    except FileNotFoundError:
        print("❌ 未找到示例数据文件，请先运行数据获取演示")


def main():
    """主演示函数"""
    print("🎯 NICE商标分类数据爬虫完整演示")
    print("=" * 60)
    print("本演示将展示以下功能:")
    print("1. 📡 在线数据获取和解析")
    print("2. 🔄 批量处理多个分类")
    print("3. 📈 数据分析和统计")
    print("4. 💾 数据导出和保存")
    
    try:
        # 演示1: 单个分类数据获取
        demo_fetch_and_parse()
        
        # 演示2: 批量获取多个分类
        demo_multiple_classes()
        
        # 演示3: 数据分析
        demo_data_analysis()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("=" * 60)
        print("📁 生成的文件:")
        print("   • sample_class2_data.json - 第2类前20个分类项")
        print("   • multiple_classes_summary.json - 多分类汇总数据")
        print("\n💡 提示:")
        print("   • 修改 db_config.json 中的数据库配置")
        print("   • 运行 python nice_spider.py 开始完整爬取")
        print("   • 使用 --classes 1 2 3 指定特定分类")
        print("   • 使用 --stats 查看数据库统计信息")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了演示")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
