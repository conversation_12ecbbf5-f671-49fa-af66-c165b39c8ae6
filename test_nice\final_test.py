#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NICE商标分类爬虫最终测试脚本
验证所有功能包括新增的重试机制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nice_spider import NiceSpider, DatabaseConfig
import time
import logging

# 设置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_complete_workflow():
    """测试完整的工作流程"""
    print("🎯 NICE商标分类爬虫完整功能测试")
    print("=" * 60)
    
    # 创建爬虫实例
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    print("📋 测试计划:")
    print("1. 测试数据获取（带重试机制）")
    print("2. 测试数据解析和清洗")
    print("3. 测试批量处理多个分类")
    print("4. 验证数据质量")
    print("5. 测试错误处理和恢复")
    
    results = {}
    
    # 测试1: 数据获取
    print(f"\n{'='*50}")
    print("🧪 测试1: 数据获取（带重试机制）")
    print(f"{'='*50}")
    
    start_time = time.time()
    html_content = spider.fetch_class_data(1)
    end_time = time.time()
    
    if html_content:
        print(f"✅ 数据获取成功")
        print(f"   耗时: {end_time - start_time:.2f}秒")
        print(f"   内容长度: {len(html_content):,} 字符")
        results['fetch'] = True
    else:
        print(f"❌ 数据获取失败")
        results['fetch'] = False
        return results
    
    # 测试2: 数据解析
    print(f"\n{'='*50}")
    print("🧪 测试2: 数据解析和清洗")
    print(f"{'='*50}")
    
    items = spider.parse_class_items(html_content, "1")
    if items:
        print(f"✅ 数据解析成功")
        print(f"   解析到 {len(items)} 个分类项")
        
        # 验证数据质量
        valid_items = 0
        for item in items:
            if (item.class_id and 
                item.class_description_en and 
                item.class_level_1 == "1" and
                item.class_type == "NICE" and
                item.version == "2025.12"):
                valid_items += 1
        
        print(f"   有效数据: {valid_items}/{len(items)} ({valid_items/len(items)*100:.1f}%)")
        
        # 显示示例数据
        print(f"   示例数据:")
        for i, item in enumerate(items[:3]):
            print(f"     {i+1}. {item.class_id}: {item.class_description_en[:50]}...")
        
        results['parse'] = True
    else:
        print(f"❌ 数据解析失败")
        results['parse'] = False
        return results
    
    # 测试3: 批量处理
    print(f"\n{'='*50}")
    print("🧪 测试3: 批量处理多个分类")
    print(f"{'='*50}")
    
    test_classes = [2, 3]  # 测试第2、3类
    batch_results = {}
    
    for class_num in test_classes:
        print(f"\n📡 处理第{class_num}类...")
        start_time = time.time()
        
        html_content = spider.fetch_class_data(class_num)
        if html_content:
            items = spider.parse_class_items(html_content, str(class_num))
            end_time = time.time()
            
            if items:
                batch_results[class_num] = {
                    'success': True,
                    'count': len(items),
                    'time': end_time - start_time
                }
                print(f"   ✅ 第{class_num}类成功: {len(items)} 个分类项，耗时 {end_time - start_time:.2f}秒")
            else:
                batch_results[class_num] = {'success': False, 'reason': 'parse_failed'}
                print(f"   ❌ 第{class_num}类解析失败")
        else:
            batch_results[class_num] = {'success': False, 'reason': 'fetch_failed'}
            print(f"   ❌ 第{class_num}类获取失败")
    
    success_count = sum(1 for r in batch_results.values() if r['success'])
    total_items = sum(r.get('count', 0) for r in batch_results.values() if r['success'])
    
    print(f"\n📊 批量处理结果:")
    print(f"   成功: {success_count}/{len(test_classes)} 个分类")
    print(f"   总数据: {total_items} 个分类项")
    
    results['batch'] = success_count == len(test_classes)
    
    # 测试4: 数据质量验证
    print(f"\n{'='*50}")
    print("🧪 测试4: 数据质量验证")
    print(f"{'='*50}")
    
    quality_checks = {
        'id_format': 0,      # ID格式正确
        'description_valid': 0,  # 描述非空
        'metadata_correct': 0,   # 元数据正确
        'text_cleaned': 0,       # 文本已清洗
    }
    
    # 使用第1类的数据进行质量检查
    sample_items = items[:50]  # 检查前50个项目

    for item in sample_items:
        # 检查ID格式（6位数字）
        if item.class_id and len(item.class_id) == 6 and item.class_id.isdigit():
            quality_checks['id_format'] += 1

        # 检查描述
        if item.class_description_en and len(item.class_description_en.strip()) > 0:
            quality_checks['description_valid'] += 1

        # 检查元数据（注意：这里使用的是第1类的数据）
        metadata_check = (item.class_type == "NICE" and
                         str(item.class_level_1) == "1" and
                         item.version == "2025.12")
        if metadata_check:
            quality_checks['metadata_correct'] += 1

        # 检查文本清洗（无多余空格）
        if (item.class_description_en and
            item.class_description_en == item.class_description_en.strip() and
            '  ' not in item.class_description_en):
            quality_checks['text_cleaned'] += 1
    
    print(f"   数据质量检查结果（样本: {len(sample_items)} 项）:")
    for check_name, count in quality_checks.items():
        percentage = count / len(sample_items) * 100
        status = "✅" if percentage >= 95 else "⚠️" if percentage >= 80 else "❌"
        print(f"     {status} {check_name}: {count}/{len(sample_items)} ({percentage:.1f}%)")
    
    avg_quality = sum(quality_checks.values()) / len(quality_checks) / len(sample_items) * 100
    results['quality'] = avg_quality >= 90
    
    # 测试5: 错误处理
    print(f"\n{'='*50}")
    print("🧪 测试5: 错误处理和恢复")
    print(f"{'='*50}")
    
    # 测试无效分类号
    print("   测试无效分类号...")
    invalid_result = spider.fetch_class_data(999)  # 不存在的分类
    if invalid_result is None:
        print("   ✅ 无效分类号正确处理")
        results['error_handling'] = True
    else:
        print("   ❌ 无效分类号处理异常")
        results['error_handling'] = False
    
    return results


def main():
    """主测试函数"""
    try:
        results = test_complete_workflow()
        
        # 输出最终结果
        print(f"\n{'='*60}")
        print("🎉 最终测试结果")
        print(f"{'='*60}")
        
        test_items = [
            ('数据获取', 'fetch'),
            ('数据解析', 'parse'),
            ('批量处理', 'batch'),
            ('数据质量', 'quality'),
            ('错误处理', 'error_handling'),
        ]
        
        passed = 0
        total = len(test_items)
        
        for test_name, key in test_items:
            if key in results:
                status = "✅ 通过" if results[key] else "❌ 失败"
                if results[key]:
                    passed += 1
            else:
                status = "⚠️ 未测试"
            print(f"{test_name}: {status}")
        
        print(f"\n总体结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("\n🎉 恭喜！所有功能测试都通过了！")
            print("💡 你的NICE商标分类爬虫已经完全就绪：")
            print("   • ✅ 网络请求稳定（带重试机制）")
            print("   • ✅ 数据解析准确")
            print("   • ✅ 批量处理高效")
            print("   • ✅ 数据质量优秀")
            print("   • ✅ 错误处理完善")
            print("\n🚀 现在可以开始正式使用了！")
            print("   1. 配置数据库连接信息")
            print("   2. 运行 python nice_spider.py")
            print("   3. 等待数据采集完成")
        else:
            print(f"\n⚠️ 有 {total - passed} 项测试未通过，请检查相关功能")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
