#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商标分类数据清洗工具 - NICE分类数据爬取和入库
支持从WIPO官网获取1-45类商标分类数据并清洗入库
"""

import requests
import re
import time
import logging
import pymysql
from bs4 import BeautifulSoup
from typing import List, Dict, Optional, Tuple, Callable, Any
from dataclasses import dataclass
from urllib.parse import urljoin
import json
import os
from datetime import datetime
from functools import wraps

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nice_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def network_retry(max_retries: int = 3, base_delay: float = 1.0):
    """
    网络请求重试装饰器

    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒），每次重试会递增
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    if attempt > 0:
                        # 递增延迟：第1次重试等待1秒，第2次等待2秒，第3次等待3秒
                        wait_time = base_delay * attempt
                        logger.info(f"网络请求重试 {attempt}/{max_retries}，等待 {wait_time:.1f} 秒...")
                        time.sleep(wait_time)

                    return func(*args, **kwargs)

                except (requests.exceptions.Timeout,
                        requests.exceptions.ConnectionError,
                        requests.exceptions.ChunkedEncodingError) as e:
                    last_exception = e
                    logger.warning(f"网络请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {type(e).__name__}: {e}")
                    if attempt == max_retries:
                        logger.error(f"网络请求最终失败，已重试 {max_retries} 次")
                        break

                except requests.exceptions.HTTPError as e:
                    # HTTP错误根据状态码决定是否重试
                    status_code = e.response.status_code if e.response else None
                    if status_code in [502, 503, 504, 429]:
                        # 服务器临时错误，可以重试
                        last_exception = e
                        logger.warning(f"HTTP临时错误 (尝试 {attempt + 1}/{max_retries + 1}): {status_code}")
                        if attempt == max_retries:
                            logger.error(f"HTTP请求最终失败: {status_code}")
                            break
                    else:
                        # 客户端错误或其他HTTP错误，不重试
                        logger.error(f"HTTP请求失败，不重试: {status_code}")
                        raise e

                except Exception as e:
                    # 其他异常不重试
                    logger.error(f"请求发生未知错误: {type(e).__name__}: {e}")
                    raise e

            # 所有重试都失败了，抛出最后一个异常
            if last_exception:
                raise last_exception
            else:
                raise Exception("网络请求失败，原因未知")

        return wrapper
    return decorator


@dataclass
class TrademarkClassItem:
    """商标分类数据项"""
    class_type: str = "NICE"  # 商标分类类型
    class_id: str = ""  # 商标分类ID (如: 010008)
    class_level_1: str = ""  # 分类层级1 (如: 1)
    class_description_en: str = ""  # 分类描述(英文)
    class_description_cn: str = ""  # 分类描述(中文) - 暂时为空
    version: str = "2025.12"  # 分类版本


class DatabaseConfig:
    """数据库配置类 - 请根据实际情况修改"""

    def __init__(self):
        # 默认配置，请根据实际情况修改
        self.host = "localhost"
        self.port = 3306
        self.user = "root"
        self.password = "your_password"
        self.database = "db_trademark_staging"
        self.charset = "utf8mb4"

    @classmethod
    def from_env(cls):
        """从环境变量读取配置"""
        config = cls()
        config.host = os.getenv('DB_HOST', config.host)
        config.port = int(os.getenv('DB_PORT', config.port))
        config.user = os.getenv('DB_USER', config.user)
        config.password = os.getenv('DB_PASSWORD', config.password)
        config.database = os.getenv('DB_DATABASE', config.database)
        return config

    @classmethod
    def from_file(cls, config_file: str = "db_config.json"):
        """从配置文件读取配置"""
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            config = cls()
            config.host = config_data.get('host', config.host)
            config.port = config_data.get('port', config.port)
            config.user = config_data.get('user', config.user)
            config.password = config_data.get('password', config.password)
            config.database = config_data.get('database', config.database)
            return config
        return cls()


class NiceSpider:
    """NICE商标分类数据爬虫"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.base_url = "https://nclpub.wipo.int/enfr/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # 分类范围：1-45类
        self.class_range = range(1, 46)

        # 数据库连接
        self.db_connection = None

    def get_db_connection(self):
        """获取数据库连接"""
        if self.db_connection is None or not self.db_connection.open:
            try:
                self.db_connection = pymysql.connect(
                    host=self.db_config.host,
                    port=self.db_config.port,
                    user=self.db_config.user,
                    password=self.db_config.password,
                    database=self.db_config.database,
                    charset=self.db_config.charset,
                    autocommit=False
                )
                logger.info("数据库连接成功")
            except Exception as e:
                logger.error(f"数据库连接失败: {e}")
                raise
        return self.db_connection

    def close_db_connection(self):
        """关闭数据库连接"""
        if self.db_connection and self.db_connection.open:
            self.db_connection.close()
            logger.info("数据库连接已关闭")

    @network_retry(max_retries=3, base_delay=1.0)
    def _fetch_class_data_with_retry(self, class_number: int) -> str:
        """
        获取指定分类的HTML数据的核心方法（带网络重试装饰器）

        Args:
            class_number: 分类号 (1-45)

        Returns:
            HTML内容字符串

        Raises:
            requests.RequestException: 网络请求异常
            ValueError: 数据格式异常
        """
        url = f"{self.base_url}?basic_numbers=show&class_number={class_number}&explanatory_notes=show&gors=&lang=en&menulang=en&mode=flat&notion=&pagination=no&version=20250101"

        logger.info(f"正在获取第{class_number}类数据: {url}")
        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'

        # 验证返回的内容
        if f"Class {class_number}" not in response.text:
            raise ValueError(f"第{class_number}类数据格式异常，未找到预期的Class标识")

        logger.info(f"第{class_number}类数据获取成功，内容长度: {len(response.text)}")
        return response.text

    def fetch_class_data(self, class_number: int) -> Optional[str]:
        """
        获取指定分类的HTML数据（公共接口）

        Args:
            class_number: 分类号 (1-45)

        Returns:
            HTML内容字符串，失败返回None
        """
        try:
            return self._fetch_class_data_with_retry(class_number)
        except Exception as e:
            logger.error(f"获取第{class_number}类数据最终失败: {type(e).__name__}: {e}")
            return None

    def parse_class_items(self, html_content: str, class_level_1: str) -> List[TrademarkClassItem]:
        """
        解析HTML内容，提取分类数据项

        Args:
            html_content: HTML内容
            class_level_1: 分类层级1 (如: "1")

        Returns:
            分类数据项列表
        """
        items = []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找所有的分类ID span标签
            # 模式: <span id="010008">010008</span>
            class_id_pattern = re.compile(r'^[0-9]{6}$')
            class_spans = soup.find_all('span', {'id': class_id_pattern})

            logger.info(f"在第{class_level_1}类中找到 {len(class_spans)} 个分类项")

            for span in class_spans:
                class_id = span.get('id', '').strip()
                if not class_id or not re.match(r'^[0-9]{6}$', class_id):
                    continue

                # 查找对应的描述
                description = self._extract_description(soup, class_id)

                if description:
                    item = TrademarkClassItem(
                        class_type="NICE",
                        class_id=class_id,
                        class_level_1=class_level_1,
                        class_description_en=description,
                        class_description_cn="",  # 暂时为空
                        version="2025.12"
                    )
                    items.append(item)
                    logger.debug(f"解析到分类项: {class_id} - {description[:50]}...")
                else:
                    logger.warning(f"未找到分类ID {class_id} 的描述")

            logger.info(f"第{class_level_1}类解析完成，共 {len(items)} 个有效分类项")
            return items

        except Exception as e:
            logger.error(f"解析第{class_level_1}类HTML内容时发生错误: {e}")
            return []

    def _extract_description(self, soup: BeautifulSoup, class_id: str) -> str:
        """
        提取指定分类ID的描述文本

        Args:
            soup: BeautifulSoup对象
            class_id: 分类ID

        Returns:
            描述文本，清理后的字符串
        """
        try:
            # 查找描述span，ID格式为: class_id + "-i1"
            desc_id = f"{class_id}-i1"
            desc_span = soup.find('span', {'id': desc_id})

            if desc_span:
                # 提取所有文本内容
                desc_texts = []

                # 获取所有子span中的文本
                child_spans = desc_span.find_all('span', style=lambda x: x and 'white-space:normal' in x)
                for child_span in child_spans:
                    text = child_span.get_text(strip=True)
                    if text:
                        desc_texts.append(text)

                # 如果没有找到子span，直接获取文本
                if not desc_texts:
                    text = desc_span.get_text(strip=True)
                    if text:
                        desc_texts.append(text)

                # 合并描述文本，用 " / " 分隔多个描述
                if desc_texts:
                    description = " / ".join(desc_texts)
                    # 清理文本
                    description = self._clean_description(description)
                    return description

            return ""

        except Exception as e:
            logger.error(f"提取分类ID {class_id} 描述时发生错误: {e}")
            return ""

    def _clean_description(self, text: str) -> str:
        """
        清理描述文本

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())

        # 移除HTML实体
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&quot;', '"')
        text = text.replace('&#39;', "'")

        return text

    def save_items_to_db(self, items: List[TrademarkClassItem]) -> bool:
        """
        批量保存分类数据到数据库

        Args:
            items: 分类数据项列表

        Returns:
            保存是否成功
        """
        if not items:
            logger.warning("没有数据需要保存")
            return True

        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # 准备SQL语句 - 使用ON DUPLICATE KEY UPDATE处理重复数据
            sql = """
            INSERT INTO trademark_class_map
            (class_type, class_id, class_level_1, class_description_en, class_description_cn, version)
            VALUES (%s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                class_type = VALUES(class_type),
                class_level_1 = VALUES(class_level_1),
                class_description_en = VALUES(class_description_en),
                class_description_cn = VALUES(class_description_cn),
                update_time = CURRENT_TIMESTAMP
            """

            # 准备批量数据
            batch_data = []
            for item in items:
                batch_data.append((
                    item.class_type,
                    item.class_id,
                    item.class_level_1,
                    item.class_description_en,
                    item.class_description_cn,
                    item.version
                ))

            # 执行批量插入
            cursor.executemany(sql, batch_data)
            conn.commit()

            logger.info(f"成功保存 {len(items)} 条分类数据到数据库")
            return True

        except Exception as e:
            logger.error(f"保存数据到数据库时发生错误: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if cursor:
                cursor.close()

    def clear_version_data(self, version: str = "2025.12") -> bool:
        """
        清理指定版本的数据

        Args:
            version: 版本号

        Returns:
            清理是否成功
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            sql = "DELETE FROM trademark_class_map WHERE version = %s"
            cursor.execute(sql, (version,))
            deleted_count = cursor.rowcount
            conn.commit()

            logger.info(f"清理版本 {version} 的数据，删除 {deleted_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"清理版本数据时发生错误: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if cursor:
                cursor.close()

    def get_statistics(self) -> Dict[str, int]:
        """
        获取数据库统计信息

        Returns:
            统计信息字典
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            stats = {}

            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM trademark_class_map")
            stats['total_count'] = cursor.fetchone()[0]

            # 按版本统计
            cursor.execute("SELECT version, COUNT(*) FROM trademark_class_map GROUP BY version")
            version_stats = cursor.fetchall()
            stats['by_version'] = {version: count for version, count in version_stats}

            # 按分类层级统计
            cursor.execute("SELECT class_level_1, COUNT(*) FROM trademark_class_map GROUP BY class_level_1 ORDER BY class_level_1")
            level_stats = cursor.fetchall()
            stats['by_level'] = {level: count for level, count in level_stats}

            return stats

        except Exception as e:
            logger.error(f"获取统计信息时发生错误: {e}")
            return {}
        finally:
            if cursor:
                cursor.close()

    def run_spider(self, class_numbers: Optional[List[int]] = None, clear_before: bool = False) -> bool:
        """
        运行爬虫，获取并保存所有分类数据

        Args:
            class_numbers: 指定要爬取的分类号列表，None表示爬取全部(1-45)
            clear_before: 是否在开始前清理现有数据

        Returns:
            爬取是否成功
        """
        try:
            # 确定要爬取的分类
            if class_numbers is None:
                class_numbers = list(self.class_range)

            logger.info(f"开始爬取商标分类数据，分类范围: {class_numbers}")

            # 清理现有数据
            if clear_before:
                logger.info("清理现有数据...")
                self.clear_version_data()

            total_items = 0
            success_classes = []
            failed_classes = []

            for class_number in class_numbers:
                try:
                    logger.info(f"处理第 {class_number} 类...")

                    # 获取HTML数据（带重试机制）
                    html_content = self.fetch_class_data(class_number)
                    if not html_content:
                        logger.error(f"第 {class_number} 类数据获取失败，已自动重试3次")
                        failed_classes.append(class_number)
                        continue

                    # 解析数据
                    items = self.parse_class_items(html_content, str(class_number))
                    if not items:
                        logger.warning(f"第 {class_number} 类没有解析到有效数据")
                        failed_classes.append(class_number)
                        continue

                    # 保存到数据库
                    if self.save_items_to_db(items):
                        total_items += len(items)
                        success_classes.append(class_number)
                        logger.info(f"第 {class_number} 类处理完成，保存 {len(items)} 条数据")
                    else:
                        failed_classes.append(class_number)
                        logger.error(f"第 {class_number} 类数据保存失败")

                    # 添加延迟，避免请求过于频繁
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"处理第 {class_number} 类时发生错误: {e}")
                    failed_classes.append(class_number)
                    continue

            # 输出统计结果
            logger.info("="*50)
            logger.info("爬取完成统计:")
            logger.info(f"成功处理的分类: {success_classes}")
            logger.info(f"失败的分类: {failed_classes}")
            logger.info(f"总共保存数据: {total_items} 条")

            # 获取数据库统计
            stats = self.get_statistics()
            if stats:
                logger.info("数据库统计信息:")
                logger.info(f"总记录数: {stats.get('total_count', 0)}")
                logger.info(f"按版本统计: {stats.get('by_version', {})}")
                logger.info(f"按分类统计: {stats.get('by_level', {})}")

            return len(failed_classes) == 0

        except Exception as e:
            logger.error(f"爬虫运行时发生错误: {e}")
            return False
        finally:
            self.close_db_connection()


def create_sample_config():
    """创建示例配置文件"""
    config = {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "your_password",
        "database": "db_trademark_staging"
    }

    with open("db_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

    print("已创建示例配置文件 db_config.json，请修改其中的数据库连接信息")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='NICE商标分类数据爬虫')
    parser.add_argument('--config', '-c', default='db_config.json', help='数据库配置文件路径')
    parser.add_argument('--classes', '-cls', nargs='+', type=int, help='指定要爬取的分类号，如: --classes 1 2 3')
    parser.add_argument('--clear', action='store_true', help='开始前清理现有数据')
    parser.add_argument('--create-config', action='store_true', help='创建示例配置文件')
    parser.add_argument('--stats', action='store_true', help='仅显示数据库统计信息')

    args = parser.parse_args()

    # 创建配置文件
    if args.create_config:
        create_sample_config()
        return

    # 加载数据库配置
    try:
        if os.path.exists(args.config):
            db_config = DatabaseConfig.from_file(args.config)
            logger.info(f"从配置文件加载数据库配置: {args.config}")
        else:
            db_config = DatabaseConfig.from_env()
            logger.info("从环境变量加载数据库配置")
    except Exception as e:
        logger.error(f"加载数据库配置失败: {e}")
        logger.info("请使用 --create-config 创建配置文件，或设置环境变量")
        return

    # 创建爬虫实例
    spider = NiceSpider(db_config)

    try:
        # 仅显示统计信息
        if args.stats:
            stats = spider.get_statistics()
            if stats:
                print("\n数据库统计信息:")
                print(f"总记录数: {stats.get('total_count', 0)}")
                print(f"按版本统计: {stats.get('by_version', {})}")
                print(f"按分类统计: {stats.get('by_level', {})}")
            else:
                print("获取统计信息失败")
            return

        # 运行爬虫
        success = spider.run_spider(
            class_numbers=args.classes,
            clear_before=args.clear
        )

        if success:
            logger.info("爬虫运行成功完成！")
        else:
            logger.error("爬虫运行过程中出现错误")

    except KeyboardInterrupt:
        logger.info("用户中断了爬虫运行")
    except Exception as e:
        logger.error(f"运行爬虫时发生未知错误: {e}")
    finally:
        spider.close_db_connection()


if __name__ == "__main__":
    main()