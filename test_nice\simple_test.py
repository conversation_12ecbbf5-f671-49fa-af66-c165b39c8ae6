#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nice_spider import <PERSON><PERSON>pider, DatabaseConfig

print("🧪 简单元数据测试")
print("=" * 40)

spider = NiceSpider(DatabaseConfig())
html = spider.fetch_class_data(1)
if html:
    items = spider.parse_class_items(html, '1')
    if items:
        print(f"解析到 {len(items)} 个项目")
        
        # 检查前10个项目
        metadata_correct = 0
        for i, item in enumerate(items[:10]):
            check = (item.class_type == "NICE" and 
                    str(item.class_level_1) == "1" and 
                    item.version == "2025.12")
            if check:
                metadata_correct += 1
            print(f"{i+1:2d}. {item.class_id}: type={item.class_type}, level={item.class_level_1}, version={item.version}, ✅" if check else f"{i+1:2d}. {item.class_id}: type={item.class_type}, level={item.class_level_1}, version={item.version}, ❌")
        
        print(f"\n元数据正确率: {metadata_correct}/10 ({metadata_correct/10*100:.1f}%)")
        
        if metadata_correct == 10:
            print("✅ 元数据检查通过！")
        else:
            print("❌ 元数据检查失败！")
else:
    print("❌ 数据获取失败")
