#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络重试机制测试脚本
测试爬虫的网络重试功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nice_spider import NiceSpider, DatabaseConfig, network_retry
import requests
import time
import logging

# 设置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_network_retry_decorator():
    """测试网络重试装饰器"""
    print("=" * 60)
    print("测试网络重试装饰器")
    print("=" * 60)
    
    # 模拟网络请求失败的函数
    call_count = 0
    
    @network_retry(max_retries=3, base_delay=0.5)
    def mock_network_request(should_fail_times: int = 2):
        nonlocal call_count
        call_count += 1
        print(f"模拟网络请求 - 第 {call_count} 次调用")
        
        if call_count <= should_fail_times:
            raise requests.exceptions.ConnectionError("模拟连接失败")
        
        return f"成功！总共调用了 {call_count} 次"
    
    try:
        # 测试1: 前2次失败，第3次成功
        print("\n🧪 测试1: 前2次失败，第3次成功")
        call_count = 0
        result = mock_network_request(should_fail_times=2)
        print(f"✅ 测试1成功: {result}")
        
        # 测试2: 全部失败
        print("\n🧪 测试2: 全部失败（超过重试次数）")
        call_count = 0
        try:
            result = mock_network_request(should_fail_times=5)
            print(f"❌ 测试2异常: 应该失败但成功了")
        except Exception as e:
            print(f"✅ 测试2成功: 正确抛出异常 - {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 装饰器测试失败: {e}")
        return False


def test_real_network_retry():
    """测试真实网络请求的重试机制"""
    print("\n" + "=" * 60)
    print("测试真实网络请求重试机制")
    print("=" * 60)
    
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 测试正常的网络请求
    print("🧪 测试正常网络请求（第1类）")
    start_time = time.time()
    html_content = spider.fetch_class_data(1)
    end_time = time.time()
    
    if html_content:
        print(f"✅ 正常请求成功，耗时: {end_time - start_time:.2f}秒")
        print(f"   内容长度: {len(html_content):,} 字符")
        return True
    else:
        print("❌ 正常请求失败")
        return False


def test_invalid_url_retry():
    """测试无效URL的重试机制"""
    print("\n" + "=" * 60)
    print("测试无效URL重试机制")
    print("=" * 60)
    
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 临时修改base_url为无效地址
    original_url = spider.base_url
    spider.base_url = "https://invalid-domain-that-does-not-exist.com/test/"
    
    print("🧪 测试无效域名请求（应该重试3次后失败）")
    start_time = time.time()
    html_content = spider.fetch_class_data(1)
    end_time = time.time()
    
    # 恢复原始URL
    spider.base_url = original_url
    
    if html_content is None:
        print(f"✅ 无效URL请求正确失败，耗时: {end_time - start_time:.2f}秒")
        print("   （应该看到3次重试的日志）")
        return True
    else:
        print("❌ 无效URL请求异常成功")
        return False


def test_timeout_retry():
    """测试超时重试机制"""
    print("\n" + "=" * 60)
    print("测试超时重试机制")
    print("=" * 60)
    
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 临时设置很短的超时时间
    original_timeout = 30
    
    # 创建一个会超时的请求
    @network_retry(max_retries=2, base_delay=0.5)
    def timeout_request():
        response = requests.get("https://httpbin.org/delay/5", timeout=1)  # 1秒超时，但服务器延迟5秒
        return response.text
    
    print("🧪 测试超时请求（1秒超时，服务器延迟5秒）")
    start_time = time.time()
    try:
        result = timeout_request()
        print("❌ 超时测试异常成功")
        return False
    except Exception as e:
        end_time = time.time()
        print(f"✅ 超时请求正确失败，耗时: {end_time - start_time:.2f}秒")
        print(f"   异常类型: {type(e).__name__}")
        return True


def test_http_error_retry():
    """测试HTTP错误重试机制"""
    print("\n" + "=" * 60)
    print("测试HTTP错误重试机制")
    print("=" * 60)
    
    # 测试不同的HTTP状态码
    test_cases = [
        (404, "应该不重试"),
        (503, "应该重试"),
        (502, "应该重试"),
    ]
    
    results = []
    
    for status_code, expected in test_cases:
        print(f"\n🧪 测试HTTP {status_code} ({expected})")
        
        @network_retry(max_retries=2, base_delay=0.3)
        def http_error_request(code):
            response = requests.get(f"https://httpbin.org/status/{code}")
            response.raise_for_status()
            return response.text
        
        start_time = time.time()
        try:
            result = http_error_request(status_code)
            print(f"❌ HTTP {status_code} 测试异常成功")
            results.append(False)
        except Exception as e:
            end_time = time.time()
            print(f"✅ HTTP {status_code} 正确失败，耗时: {end_time - start_time:.2f}秒")
            print(f"   异常类型: {type(e).__name__}")
            results.append(True)
    
    return all(results)


def main():
    """主测试函数"""
    print("🔄 网络重试机制测试")
    print("=" * 60)
    
    tests = [
        ("网络重试装饰器", test_network_retry_decorator),
        ("真实网络请求", test_real_network_retry),
        ("无效URL重试", test_invalid_url_retry),
        ("超时重试机制", test_timeout_retry),
        ("HTTP错误重试", test_http_error_retry),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("重试机制测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有重试机制测试都通过了！")
        print("💡 网络重试功能已正确实现：")
        print("   • 自动重试网络连接错误")
        print("   • 自动重试超时错误")
        print("   • 智能处理HTTP错误（502/503/504重试，404/403不重试）")
        print("   • 递增延迟策略（1秒、2秒、3秒）")
        print("   • 详细的重试日志记录")
    else:
        print("⚠️  部分重试机制测试失败，请检查相关功能")


if __name__ == "__main__":
    main()
