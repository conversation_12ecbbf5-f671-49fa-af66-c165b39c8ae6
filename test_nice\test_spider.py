#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NICE商标分类爬虫测试脚本
用于测试爬虫的各个功能模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nice_spider import NiceSpider, DatabaseConfig, TrademarkClassItem
import logging

# 设置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_fetch_data():
    """测试数据获取功能"""
    print("=" * 50)
    print("测试数据获取功能")
    print("=" * 50)
    
    # 创建配置（使用默认配置，不连接数据库）
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 测试获取第1类数据
    html_content = spider.fetch_class_data(1)
    
    if html_content:
        print(f"✅ 成功获取第1类数据，内容长度: {len(html_content)}")
        
        # 测试解析功能
        items = spider.parse_class_items(html_content, "1")
        print(f"✅ 成功解析数据，共 {len(items)} 个分类项")
        
        # 显示前5个项目
        print("\n前5个分类项:")
        for i, item in enumerate(items[:5]):
            print(f"{i+1}. {item.class_id}: {item.class_description_en}")
        
        return True
    else:
        print("❌ 获取数据失败")
        return False


def test_parse_local_file():
    """测试解析本地HTML文件"""
    print("\n" + "=" * 50)
    print("测试解析本地HTML文件")
    print("=" * 50)
    
    html_file = "class.html"
    if not os.path.exists(html_file):
        print(f"❌ 本地文件 {html_file} 不存在")
        return False
    
    # 创建爬虫实例
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 读取本地HTML文件
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"✅ 成功读取本地文件，内容长度: {len(html_content)}")
    
    # 解析数据
    items = spider.parse_class_items(html_content, "1")
    print(f"✅ 成功解析数据，共 {len(items)} 个分类项")
    
    # 显示前10个项目
    print("\n前10个分类项:")
    for i, item in enumerate(items[:10]):
        print(f"{i+1:2d}. {item.class_id}: {item.class_description_en}")
    
    # 显示一些统计信息
    print(f"\n统计信息:")
    print(f"- 总分类项数: {len(items)}")
    print(f"- 最短描述长度: {min(len(item.class_description_en) for item in items) if items else 0}")
    print(f"- 最长描述长度: {max(len(item.class_description_en) for item in items) if items else 0}")
    
    return True


def test_data_cleaning():
    """测试数据清洗功能"""
    print("\n" + "=" * 50)
    print("测试数据清洗功能")
    print("=" * 50)
    
    db_config = DatabaseConfig()
    spider = NiceSpider(db_config)
    
    # 测试文本清洗
    test_texts = [
        "  acetate of cellulose, unprocessed  ",
        "activated carbon\n/ activated charcoal",
        "additives, chemical, to drilling muds / chemical additives to drilling muds",
        "acidulated water for recharging batteries / acidulated water for recharging accumulators",
        "  multiple   spaces   text  "
    ]
    
    print("文本清洗测试:")
    for i, text in enumerate(test_texts):
        cleaned = spider._clean_description(text)
        print(f"{i+1}. 原文: '{text}'")
        print(f"   清洗后: '{cleaned}'")
        print()
    
    return True


def main():
    """主测试函数"""
    print("NICE商标分类爬虫功能测试")
    print("=" * 60)
    
    tests = [
        ("数据清洗功能", test_data_cleaning),
        ("本地文件解析", test_parse_local_file),
        ("在线数据获取", test_fetch_data),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查相关功能")


if __name__ == "__main__":
    main()
