create table db_trademark_staging.trademark_class_map
(
    id                   bigint auto_increment
        primary key,
    class_type           varchar(10) default ''                not null comment '商标分类类型',
    class_id             varchar(30) default ''                not null comment '商标分类ID',
    class_level_1        varchar(10) default ''                not null comment '分类层级1',
    class_description_en longtext                              not null comment '分类描述(en)',
    class_description_cn longtext                              not null comment '分类描述(cn)',
    version              varchar(10) default ''                not null comment '分类版本',
    create_time          datetime    default CURRENT_TIMESTAMP not null comment '创建日期',
    update_time          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新日期',
    constraint uni_class_id
        unique (class_id, version)
)
    comment '商标分类(NICE/VIENNA)配置表';