# 专利数据线上表结构设计

## 设计概述

基于 SwissReg 专利数据源的分析，设计统一的线上专利数据表结构。按照四个核心维度进行设计：
1. **专利基础信息表** - 存储专利的核心信息
2. **专利公告发布表** - 存储专利相关的官方公告和历史事件
3. **专利所属信息表** - 存储专利的申请人、发明人、代理人等所属关系
4. **专利分类信息表** - 存储专利的各种分类信息（IPC、CPC等）

## 1. 专利基础信息表 (patent_basic_info)

```sql
CREATE TABLE patent_basic_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    patent_id VARCHAR(100) NOT NULL COMMENT '统一专利ID（全局唯一）',
    source_type ENUM('SWISSREG', 'USPTO', 'EPO', 'WIPO', 'OTHER') NOT NULL COMMENT '数据源类型',
    source_patent_id VARCHAR(100) NOT NULL COMMENT '原始数据源中的专利ID',
    
    -- 基础标识信息
    application_number VARCHAR(100) DEFAULT '' COMMENT '申请号',
    patent_number VARCHAR(100) DEFAULT '' COMMENT '专利号/授权号',
    publication_number VARCHAR(100) DEFAULT '' COMMENT '公开号',
    
    -- 专利类型和状态
    ip_type VARCHAR(100) DEFAULT '' COMMENT '知识产权类型',
    patent_type ENUM('INVENTION', 'UTILITY_MODEL', 'DESIGN', 'OTHER') DEFAULT 'INVENTION' COMMENT '专利类型',
    current_status VARCHAR(100) DEFAULT '' COMMENT '当前状态',
    
    -- 重要日期
    application_date VARCHAR(20) DEFAULT '' COMMENT '申请日期',
    publication_date VARCHAR(20) DEFAULT '' COMMENT '公开日期',
    grant_date VARCHAR(20) DEFAULT '' COMMENT '授权日期',
    next_renewal_date VARCHAR(20) DEFAULT '' COMMENT '下次续费日期',
    protection_end_date VARCHAR(20) DEFAULT '' COMMENT '保护期结束日期',
    
    -- 语言和地理信息
    application_language VARCHAR(10) DEFAULT '' COMMENT '申请语言',
    filing_country VARCHAR(10) DEFAULT '' COMMENT '申请国家代码',
    
    -- 专利内容
    title LONGTEXT COMMENT '专利标题',
    abstract LONGTEXT COMMENT '专利摘要',
    description LONGTEXT COMMENT '专利说明书',
    claims LONGTEXT COMMENT '权利要求',
    patent_specifications LONGTEXT COMMENT '专利规格说明',
    
    -- 国际化信息
    international_registration VARCHAR(100) DEFAULT '' COMMENT '国际注册号',
    international_publication VARCHAR(100) DEFAULT '' COMMENT '国际公开号',
    priorities LONGTEXT COMMENT '优先权信息（JSON格式）',
    
    -- 特殊标记
    an_inheritance TINYINT(1) DEFAULT 0 COMMENT '遗传/传统标识（0:否, 1:是）',
    exhibition_immunity VARCHAR(100) DEFAULT '' COMMENT '展览豁免权',
    paediatric_spc VARCHAR(100) DEFAULT '' COMMENT '儿科SPC信息',
    
    -- 关联专利
    base_patent VARCHAR(100) DEFAULT '' COMMENT '基础专利申请号',
    parent_patent_id VARCHAR(100) DEFAULT '' COMMENT '母案专利ID',
    
    -- 状态变更
    cancellation VARCHAR(255) DEFAULT '' COMMENT '撤销信息',
    
    -- 扩展信息
    extra_data JSON COMMENT '扩展数据（JSON格式）',
    
    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效（软删除标记）',
    data_version INT DEFAULT 1 COMMENT '数据版本号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_patent_id (patent_id),
    UNIQUE KEY uk_source_patent (source_type, source_patent_id),
    INDEX idx_application_number (application_number),
    INDEX idx_patent_number (patent_number),
    INDEX idx_publication_number (publication_number),
    INDEX idx_current_status (current_status),
    INDEX idx_application_date (application_date),
    INDEX idx_grant_date (grant_date),
    INDEX idx_title (title(100)),
    INDEX idx_create_time (create_time),
    INDEX idx_update_time (update_time)
) COMMENT='专利基础信息表';
```

## 2. 专利公告发布表 (patent_publications)

```sql
CREATE TABLE patent_publications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    publication_id VARCHAR(100) NOT NULL COMMENT '公告ID（全局唯一）',
    patent_id VARCHAR(100) NOT NULL COMMENT '关联的专利ID',
    source_type ENUM('SWISSREG', 'USPTO', 'EPO', 'WIPO', 'OTHER') NOT NULL COMMENT '数据源类型',
    
    -- 公告基础信息
    publication_number VARCHAR(100) DEFAULT '' COMMENT '公告号',
    publication_type ENUM('APPLICATION', 'GRANT', 'AMENDMENT', 'CANCELLATION', 'RENEWAL', 'OTHER') DEFAULT 'APPLICATION' COMMENT '公告类型',
    
    -- 公告内容
    publication_text LONGTEXT COMMENT '公告文本内容',
    publication_doc VARCHAR(500) DEFAULT '' COMMENT '公告文档路径',
    publication_mark LONGTEXT COMMENT '公告备注',
    
    -- 变更信息
    change_reason VARCHAR(255) DEFAULT '' COMMENT '变更原因',
    old_data LONGTEXT COMMENT '变更前数据',
    new_data LONGTEXT COMMENT '变更后数据',
    modification_info LONGTEXT COMMENT '修改信息详情',
    
    -- 日期信息
    publication_date VARCHAR(20) DEFAULT '' COMMENT '公告发布日期',
    change_date VARCHAR(20) DEFAULT '' COMMENT '变更日期',
    effective_date VARCHAR(20) DEFAULT '' COMMENT '生效日期',
    
    -- 引用信息
    image_refs VARCHAR(500) DEFAULT '' COMMENT '图片引用',
    protection_title_ref VARCHAR(255) DEFAULT '' COMMENT '保护标题引用',
    document_refs LONGTEXT COMMENT '相关文档引用',
    
    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_publication_id (publication_id),
    INDEX idx_patent_id (patent_id),
    INDEX idx_publication_number (publication_number),
    INDEX idx_publication_type (publication_type),
    INDEX idx_publication_date (publication_date),
    INDEX idx_change_date (change_date),
    INDEX idx_create_time (create_time),
    
    -- 外键约束
    FOREIGN KEY fk_patent_publications_patent_id (patent_id)
        REFERENCES patent_basic_info(patent_id) ON DELETE CASCADE
) COMMENT='专利公告发布表';
```

## 3. 专利所属信息表 (patent_ownership)

```sql
CREATE TABLE patent_ownership (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    ownership_id VARCHAR(100) NOT NULL COMMENT '所属关系ID（全局唯一）',
    patent_id VARCHAR(100) NOT NULL COMMENT '关联的专利ID',
    source_type ENUM('SWISSREG', 'USPTO', 'EPO', 'WIPO', 'OTHER') NOT NULL COMMENT '数据源类型',

    -- 所属关系类型
    relationship_type ENUM('APPLICANT', 'INVENTOR', 'OWNER', 'AGENT', 'ATTORNEY', 'CORRESPONDENCE') NOT NULL COMMENT '关系类型',
    entity_type ENUM('INDIVIDUAL', 'COMPANY', 'ORGANIZATION', 'GOVERNMENT') DEFAULT 'INDIVIDUAL' COMMENT '实体类型',

    -- 身份标识
    owner_id VARCHAR(100) DEFAULT '' COMMENT '所有者ID',
    entity_identifier VARCHAR(100) DEFAULT '' COMMENT '实体标识ID',
    owner_type VARCHAR(100) DEFAULT '' COMMENT '所有者类型',

    -- 基础信息
    name VARCHAR(500) DEFAULT '' COMMENT '姓名/公司名称',
    additional_name VARCHAR(500) DEFAULT '' COMMENT '附加名称',
    display_name VARCHAR(500) DEFAULT '' COMMENT '显示名称',
    title VARCHAR(100) DEFAULT '' COMMENT '职称/头衔',

    -- 地址信息
    street VARCHAR(500) DEFAULT '' COMMENT '街道地址',
    additional_street VARCHAR(500) DEFAULT '' COMMENT '附加街道信息',
    house_number VARCHAR(100) DEFAULT '' COMMENT '门牌号',
    zip_code VARCHAR(50) DEFAULT '' COMMENT '邮政编码',
    town VARCHAR(200) DEFAULT '' COMMENT '城市',
    country VARCHAR(100) DEFAULT '' COMMENT '国家',
    country_code VARCHAR(10) DEFAULT '' COMMENT '国家代码',

    -- 联系信息
    email VARCHAR(200) DEFAULT '' COMMENT '电子邮箱',
    phone_number VARCHAR(50) DEFAULT '' COMMENT '电话号码',
    fax_number VARCHAR(50) DEFAULT '' COMMENT '传真号码',

    -- 语言和修改信息
    language VARCHAR(10) DEFAULT '' COMMENT '语言代码',
    modification LONGTEXT COMMENT '修改信息',

    -- 优先级和状态
    priority_order INT DEFAULT 1 COMMENT '优先级顺序',
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否为主要联系人',
    status ENUM('ACTIVE', 'INACTIVE', 'TRANSFERRED') DEFAULT 'ACTIVE' COMMENT '关系状态',

    -- 有效期
    effective_date VARCHAR(20) DEFAULT '' COMMENT '生效日期',
    expiry_date VARCHAR(20) DEFAULT '' COMMENT '失效日期',

    -- 扩展信息
    additional_info JSON COMMENT '额外信息（JSON格式）',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_ownership_id (ownership_id),
    UNIQUE KEY uk_patent_entity_relation (patent_id, entity_identifier, relationship_type),
    INDEX idx_patent_id (patent_id),
    INDEX idx_relationship_type (relationship_type),
    INDEX idx_entity_type (entity_type),
    INDEX idx_name (name(100)),
    INDEX idx_country_code (country_code),
    INDEX idx_create_time (create_time),

    -- 外键约束
    FOREIGN KEY fk_patent_ownership_patent_id (patent_id)
        REFERENCES patent_basic_info(patent_id) ON DELETE CASCADE
) COMMENT='专利所属信息表';
```

## 4. 专利分类信息表 (patent_classifications)

```sql
CREATE TABLE patent_classifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    classification_id VARCHAR(100) NOT NULL COMMENT '分类关系ID（全局唯一）',
    patent_id VARCHAR(100) NOT NULL COMMENT '关联的专利ID',
    source_type ENUM('SWISSREG', 'USPTO', 'EPO', 'WIPO', 'OTHER') NOT NULL COMMENT '数据源类型',

    -- 分类体系信息
    classification_system ENUM('IPC', 'CPC', 'USPC', 'FI', 'F_TERM', 'OTHER') NOT NULL COMMENT '分类体系类型',
    classification_version VARCHAR(20) DEFAULT '' COMMENT '分类版本',

    -- 分类代码和描述
    class_code VARCHAR(50) NOT NULL COMMENT '分类代码',
    class_symbol VARCHAR(100) DEFAULT '' COMMENT '分类符号',
    subclass VARCHAR(50) DEFAULT '' COMMENT '子分类',
    main_group VARCHAR(50) DEFAULT '' COMMENT '主组',
    subgroup VARCHAR(50) DEFAULT '' COMMENT '子组',

    -- 分类日期
    classification_date VARCHAR(20) DEFAULT '' COMMENT '分类日期',
    effective_date VARCHAR(20) DEFAULT '' COMMENT '生效日期',

    -- 分类层级（用于层级结构）
    parent_class_code VARCHAR(50) DEFAULT '' COMMENT '父级分类代码',
    level_depth INT DEFAULT 1 COMMENT '分类层级深度',

    -- 分类属性
    classification_type ENUM('MAIN', 'ADDITIONAL', 'INVENTIVE', 'NON_INVENTIVE') DEFAULT 'MAIN' COMMENT '分类类型',
    classification_status ENUM('ACTIVE', 'INACTIVE', 'PENDING', 'DELETED') DEFAULT 'ACTIVE' COMMENT '分类状态',

    -- 多语言描述
    description_en LONGTEXT COMMENT '英文描述',
    description_zh LONGTEXT COMMENT '中文描述',
    description_de LONGTEXT COMMENT '德文描述',
    description_fr LONGTEXT COMMENT '法文描述',
    description_ja LONGTEXT COMMENT '日文描述',

    -- 状态和优先级
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否为主要分类',
    priority_order INT DEFAULT 1 COMMENT '优先级顺序',

    -- 扩展信息
    additional_attributes JSON COMMENT '额外属性（JSON格式）',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_classification_id (classification_id),
    UNIQUE KEY uk_patent_class_system (patent_id, classification_system, class_code),
    INDEX idx_patent_id (patent_id),
    INDEX idx_classification_system (classification_system),
    INDEX idx_class_code (class_code),
    INDEX idx_classification_date (classification_date),
    INDEX idx_parent_class_code (parent_class_code),
    INDEX idx_create_time (create_time),

    -- 外键约束
    FOREIGN KEY fk_patent_classifications_patent_id (patent_id)
        REFERENCES patent_basic_info(patent_id) ON DELETE CASCADE
) COMMENT='专利分类信息表';
```

## 5. 辅助表设计

### 5.1 专利文档信息表 (patent_documents)

```sql
CREATE TABLE patent_documents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    document_id VARCHAR(100) NOT NULL COMMENT '文档ID（全局唯一）',
    patent_id VARCHAR(100) NOT NULL COMMENT '关联的专利ID',
    source_type ENUM('SWISSREG', 'USPTO', 'EPO', 'WIPO', 'OTHER') NOT NULL COMMENT '数据源类型',

    -- 文档基础信息
    document_type ENUM('SPECIFICATION', 'CLAIMS', 'DRAWINGS', 'ABSTRACT', 'SEARCH_REPORT', 'EXAMINATION_REPORT', 'OTHER') NOT NULL COMMENT '文档类型',
    document_name VARCHAR(255) DEFAULT '' COMMENT '文档名称',
    document_format VARCHAR(20) DEFAULT '' COMMENT '文档格式（pdf/xml/html等）',

    -- 文档存储
    original_url VARCHAR(1000) DEFAULT '' COMMENT '原始文档URL',
    local_path VARCHAR(500) DEFAULT '' COMMENT '本地存储路径',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小（字节）',

    -- 文档属性
    language VARCHAR(10) DEFAULT '' COMMENT '文档语言',
    page_count INT DEFAULT 0 COMMENT '页数',
    version VARCHAR(20) DEFAULT '' COMMENT '版本号',

    -- 状态信息
    status ENUM('ACTIVE', 'INACTIVE', 'PROCESSING', 'ERROR') DEFAULT 'ACTIVE' COMMENT '文档状态',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_document_id (document_id),
    INDEX idx_patent_id (patent_id),
    INDEX idx_document_type (document_type),
    INDEX idx_create_time (create_time),

    -- 外键约束
    FOREIGN KEY fk_patent_documents_patent_id (patent_id)
        REFERENCES patent_basic_info(patent_id) ON DELETE CASCADE
) COMMENT='专利文档信息表';
```

### 5.2 数据源映射表 (patent_source_mapping)

```sql
CREATE TABLE patent_source_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    patent_id VARCHAR(100) NOT NULL COMMENT '统一专利ID',
    source_type ENUM('SWISSREG', 'USPTO', 'EPO', 'WIPO', 'OTHER') NOT NULL COMMENT '数据源类型',
    source_patent_id VARCHAR(100) NOT NULL COMMENT '原始数据源专利ID',
    source_table_name VARCHAR(100) DEFAULT '' COMMENT '源表名称',

    -- 数据质量信息
    data_quality_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '数据质量评分（0-100）',
    completeness_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '数据完整性评分（0-100）',
    last_sync_time DATETIME COMMENT '最后同步时间',
    sync_status ENUM('SUCCESS', 'FAILED', 'PENDING', 'PARTIAL') DEFAULT 'PENDING' COMMENT '同步状态',

    -- 冲突处理
    conflict_resolution ENUM('AUTO', 'MANUAL', 'PRIORITY') DEFAULT 'AUTO' COMMENT '冲突解决方式',
    priority_level INT DEFAULT 1 COMMENT '数据源优先级（数字越小优先级越高）',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_source_mapping (source_type, source_patent_id),
    INDEX idx_patent_id (patent_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_time (last_sync_time),

    -- 外键约束
    FOREIGN KEY fk_source_mapping_patent_id (patent_id)
        REFERENCES patent_basic_info(patent_id) ON DELETE CASCADE
) COMMENT='数据源映射表';
```

## 6. 设计说明和特性

### 6.1 核心设计原则

1. **统一标识**: 使用全局唯一的 `patent_id` 作为所有表的关联键
2. **数据源兼容**: 通过 `source_type` 字段区分不同数据源，支持多专利局数据整合
3. **专利特色**: 针对专利数据特点设计IPC/CPC分类、优先权、续费等专业字段
4. **扩展性**: 使用 JSON 字段存储扩展数据，便于未来功能扩展
5. **数据完整性**: 通过外键约束保证数据一致性
6. **软删除**: 使用 `is_active` 字段实现软删除，保护历史数据

### 6.2 专利数据特点

- **分类体系**: 支持IPC、CPC、USPC等多种国际专利分类体系
- **时间管理**: 重点关注申请日、公开日、授权日、续费日等关键时间节点
- **多角色**: 区分发明人、申请人、专利权人、代理人等不同角色
- **文档管理**: 专门的文档表管理说明书、权利要求书、附图等专利文档
- **国际化**: 支持PCT国际申请、优先权声明等国际专利特色

### 6.3 表关系说明

```
patent_basic_info (1)
    ├── patent_publications (N) - 一个专利可以有多个公告
    ├── patent_ownership (N) - 一个专利可以有多个所属关系
    ├── patent_classifications (N) - 一个专利可以有多个分类
    ├── patent_documents (N) - 一个专利可以有多个文档
    └── patent_source_mapping (N) - 一个专利可以来自多个数据源
```

## 7. 数据迁移建议

### 7.1 SwissReg 数据源映射

| 原表 | 目标表 | 主要字段映射 |
|------|--------|-------------|
| swissreg_patent_info | patent_basic_info | open_id→patent_id, application_number→application_number |
| swissreg_patent_history | patent_publications | open_id→patent_id, publication_id→publication_number |
| swissreg_patent_registered | patent_ownership | open_id→patent_id, owner_type→relationship_type |
| swissreg_patent_ipc | patent_classifications | open_id→patent_id, ipc_id→class_code |
| swissreg_patent_cpc | patent_classifications | open_id→patent_id, cpc_id→class_code |

### 7.2 字段映射详情

#### 基础信息映射
- `open_id` → `source_patent_id`
- `application_number` → `application_number`
- `patent_id` → `patent_number`
- `application_date` → `application_date`
- `publication_date` → `publication_date`
- `grant_date` → `grant_date`
- `status` → `current_status`

#### 分类信息映射
- `ipc_id` → `class_code` (classification_system='IPC')
- `cpc_id` → `class_code` (classification_system='CPC')
- `ipc_date`/`cpc_date` → `classification_date`

## 8. 使用建议

### 8.1 查询优化

1. **基础查询**: 优先使用 `patent_id` 进行关联查询
2. **分类查询**: 根据分类体系类型进行索引查询
3. **状态过滤**: 始终添加 `is_active = 1` 条件过滤软删除数据
4. **日期范围**: 使用申请日期、授权日期进行时间范围查询

### 8.2 数据维护

1. **定期同步**: 建立定时任务同步各专利局的更新数据
2. **分类更新**: 定期更新IPC/CPC分类版本和描述
3. **状态监控**: 监控专利状态变化，及时更新续费提醒
4. **质量控制**: 监控数据质量评分，处理数据异常

### 8.3 性能优化

1. **分区策略**: 可考虑按申请年份进行表分区
2. **缓存策略**: 对热点专利数据和分类信息进行缓存
3. **读写分离**: 查询和写入操作分离，提高并发性能
4. **批量操作**: 数据迁移时使用批量插入提高效率

---

**设计完成时间**: 2025-07-21
**版本**: v1.0
**设计者**: Augment Agent 🐾
```
```
