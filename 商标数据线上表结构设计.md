# 商标数据线上表结构设计

## 设计概述

基于 WIPO 和 SwissReg 两个数据源的分析，设计统一的线上商标数据表结构。按照四个核心维度进行设计：
1. **商标基础信息表** - 存储商标的核心信息
2. **商标公告发布表** - 存储商标相关的官方公告和事件
3. **商标所属信息表** - 存储商标的申请人、代理人等所属关系
4. **商标分类信息表** - 存储商标的各种分类信息

## 1. 商标基础信息表 (trademark_basic_info)

```sql
CREATE TABLE trademark_basic_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    trademark_id VARCHAR(100) NOT NULL COMMENT '统一商标ID（全局唯一）',
    source_type ENUM('WIPO', 'SWISSREG', 'OTHER') NOT NULL COMMENT '数据源类型',
    source_trademark_id VARCHAR(100) NOT NULL COMMENT '原始数据源中的商标ID',
    
    -- 基础标识信息
    brand_name LONGTEXT COMMENT '商标名称/品牌名称',
    application_number VARCHAR(100) DEFAULT '' COMMENT '申请号',
    registration_number VARCHAR(100) DEFAULT '' COMMENT '注册号',
    
    -- 商标类型和特征
    trademark_type VARCHAR(100) DEFAULT '' COMMENT '商标类型（文字/图形/组合等）',
    trademark_category VARCHAR(100) DEFAULT '' COMMENT '商标类别',
    mark_feature VARCHAR(255) DEFAULT '' COMMENT '商标特征描述',
    kind VARCHAR(100) DEFAULT '' COMMENT '标记种类',
    
    -- 状态信息
    current_status VARCHAR(100) DEFAULT '' COMMENT '当前状态',
    office_status VARCHAR(100) DEFAULT '' COMMENT '官方状态',
    gbd_status VARCHAR(100) DEFAULT '' COMMENT '官方公报状态',
    
    -- 重要日期
    application_date VARCHAR(20) DEFAULT '' COMMENT '申请日期',
    registration_date VARCHAR(20) DEFAULT '' COMMENT '注册日期',
    publication_date VARCHAR(20) DEFAULT '' COMMENT '公布日期',
    expiry_date VARCHAR(20) DEFAULT '' COMMENT '到期日期',
    status_date VARCHAR(20) DEFAULT '' COMMENT '状态更新日期',
    
    -- 地理信息
    filing_place VARCHAR(255) DEFAULT '' COMMENT '申请地/备案地',
    registration_office_code VARCHAR(100) DEFAULT '' COMMENT '注册办公室代码',
    office VARCHAR(100) DEFAULT '' COMMENT '商标注册办公室',
    designated_countries TEXT COMMENT '指定国家列表',
    
    -- 图像和描述
    logo_url VARCHAR(500) DEFAULT '' COMMENT '商标图片URL',
    logo_small_url VARCHAR(500) DEFAULT '' COMMENT '小尺寸商标图片URL',
    logo_big_url VARCHAR(500) DEFAULT '' COMMENT '大尺寸商标图片URL',
    word_mark_text LONGTEXT COMMENT '文字商标规范文本',
    word_mark_language VARCHAR(10) DEFAULT '' COMMENT '文字商标语言代码',
    description LONGTEXT COMMENT '商标描述',
    designation VARCHAR(2000) DEFAULT '' COMMENT '商标指定使用说明',
    
    -- 颜色相关
    colour_indicator VARCHAR(100) DEFAULT '' COMMENT '颜色标识',
    colour_claim LONGTEXT COMMENT '颜色声明',
    colour_claimed LONGTEXT COMMENT '声明的颜色',
    
    -- 语言信息
    application_language VARCHAR(10) DEFAULT '' COMMENT '申请语言代码',
    
    -- 扩展信息
    collection VARCHAR(255) DEFAULT '' COMMENT '商标系列/收藏标识',
    acquired_distinctiveness LONGTEXT COMMENT '获得的显著性',
    disclaimer_text LONGTEXT COMMENT '免责声明文本',
    disclaimer_language VARCHAR(10) DEFAULT '' COMMENT '免责声明语言',
    score DECIMAL(10,4) DEFAULT 0.0000 COMMENT '商标评分',
    extra_data JSON COMMENT '扩展数据（JSON格式）',
    
    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效（软删除标记）',
    data_version INT DEFAULT 1 COMMENT '数据版本号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_trademark_id (trademark_id),
    UNIQUE KEY uk_source_trademark (source_type, source_trademark_id),
    INDEX idx_application_number (application_number),
    INDEX idx_registration_number (registration_number),
    INDEX idx_brand_name (brand_name(100)),
    INDEX idx_current_status (current_status),
    INDEX idx_application_date (application_date),
    INDEX idx_create_time (create_time),
    INDEX idx_update_time (update_time)
) COMMENT='商标基础信息表';
```

## 2. 商标公告发布表 (trademark_publications)

```sql
CREATE TABLE trademark_publications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    publication_id VARCHAR(100) NOT NULL COMMENT '公告ID（全局唯一）',
    trademark_id VARCHAR(100) NOT NULL COMMENT '关联的商标ID',
    source_type ENUM('WIPO', 'SWISSREG', 'OTHER') NOT NULL COMMENT '数据源类型',
    
    -- 公告基础信息
    publication_number VARCHAR(100) DEFAULT '' COMMENT '公告号',
    publication_type VARCHAR(100) DEFAULT '' COMMENT '公告类型',
    office_kind VARCHAR(255) DEFAULT '' COMMENT '官方处理动作类型',
    gbd_kind VARCHAR(255) DEFAULT '' COMMENT '官方公报数据类型',
    
    -- 公告内容
    publication_text LONGTEXT COMMENT '公告文本内容',
    publication_doc LONGTEXT COMMENT '公告文档',
    publication_mark LONGTEXT COMMENT '公告备注',
    
    -- 变更信息（主要用于SwissReg）
    change_reason VARCHAR(255) DEFAULT '' COMMENT '变更原因',
    old_data LONGTEXT COMMENT '变更前数据',
    new_data LONGTEXT COMMENT '变更后数据',
    
    -- 日期信息
    publication_date VARCHAR(20) DEFAULT '' COMMENT '公告发布日期',
    event_date VARCHAR(20) DEFAULT '' COMMENT '事件发生日期',
    change_date VARCHAR(20) DEFAULT '' COMMENT '变更日期',
    
    -- 引用信息
    image_refs VARCHAR(500) DEFAULT '' COMMENT '图片引用',
    protection_title_ref VARCHAR(255) DEFAULT '' COMMENT '保护标题引用',
    
    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_publication_id (publication_id),
    INDEX idx_trademark_id (trademark_id),
    INDEX idx_publication_number (publication_number),
    INDEX idx_publication_type (publication_type),
    INDEX idx_publication_date (publication_date),
    INDEX idx_event_date (event_date),
    INDEX idx_create_time (create_time),
    
    -- 外键约束
    FOREIGN KEY fk_trademark_publications_trademark_id (trademark_id)
        REFERENCES trademark_basic_info(trademark_id) ON DELETE CASCADE
) COMMENT='商标公告发布表';
```

## 3. 商标所属信息表 (trademark_ownership)

```sql
CREATE TABLE trademark_ownership (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    ownership_id VARCHAR(100) NOT NULL COMMENT '所属关系ID（全局唯一）',
    trademark_id VARCHAR(100) NOT NULL COMMENT '关联的商标ID',
    source_type ENUM('WIPO', 'SWISSREG', 'OTHER') NOT NULL COMMENT '数据源类型',

    -- 所属关系类型
    relationship_type ENUM('APPLICANT', 'OWNER', 'REPRESENTATIVE', 'CORRESPONDENCE', 'AGENT') NOT NULL COMMENT '关系类型',
    entity_type ENUM('INDIVIDUAL', 'COMPANY', 'ORGANIZATION', 'GOVERNMENT') DEFAULT 'INDIVIDUAL' COMMENT '实体类型',

    -- 身份标识
    entity_identifier VARCHAR(100) DEFAULT '' COMMENT '实体标识ID',
    kind VARCHAR(100) DEFAULT '' COMMENT '分类/种类',
    address_type VARCHAR(50) DEFAULT '' COMMENT '地址类型（如INHABER/VERTRETER）',

    -- 基础信息
    full_name LONGTEXT COMMENT '完整姓名/公司名称',
    display_name VARCHAR(500) DEFAULT '' COMMENT '显示名称',

    -- 地址信息
    full_address LONGTEXT COMMENT '完整地址',
    street VARCHAR(500) DEFAULT '' COMMENT '街道地址',
    city VARCHAR(200) DEFAULT '' COMMENT '城市',
    state_province VARCHAR(200) DEFAULT '' COMMENT '州/省',
    zip_code VARCHAR(50) DEFAULT '' COMMENT '邮政编码',
    country_code VARCHAR(10) DEFAULT '' COMMENT '国家代码',
    country_name VARCHAR(100) DEFAULT '' COMMENT '国家名称',

    -- 联系信息
    phone VARCHAR(50) DEFAULT '' COMMENT '电话号码',
    email VARCHAR(200) DEFAULT '' COMMENT '电子邮箱',
    fax VARCHAR(50) DEFAULT '' COMMENT '传真号码',
    website VARCHAR(500) DEFAULT '' COMMENT '网站地址',

    -- 优先级和状态
    priority_order INT DEFAULT 1 COMMENT '优先级顺序（同一商标多个相同关系类型时）',
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否为主要联系人',
    status ENUM('ACTIVE', 'INACTIVE', 'TRANSFERRED') DEFAULT 'ACTIVE' COMMENT '关系状态',

    -- 有效期
    effective_date VARCHAR(20) DEFAULT '' COMMENT '生效日期',
    expiry_date VARCHAR(20) DEFAULT '' COMMENT '失效日期',

    -- 扩展信息
    additional_info JSON COMMENT '额外信息（JSON格式）',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_ownership_id (ownership_id),
    UNIQUE KEY uk_trademark_entity_relation (trademark_id, entity_identifier, relationship_type),
    INDEX idx_trademark_id (trademark_id),
    INDEX idx_relationship_type (relationship_type),
    INDEX idx_entity_type (entity_type),
    INDEX idx_full_name (full_name(100)),
    INDEX idx_country_code (country_code),
    INDEX idx_create_time (create_time),

    -- 外键约束
    FOREIGN KEY fk_trademark_ownership_trademark_id (trademark_id)
        REFERENCES trademark_basic_info(trademark_id) ON DELETE CASCADE
) COMMENT='商标所属信息表';
```

## 4. 商标分类信息表 (trademark_classifications)

```sql
CREATE TABLE trademark_classifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    classification_id VARCHAR(100) NOT NULL COMMENT '分类关系ID（全局唯一）',
    trademark_id VARCHAR(100) NOT NULL COMMENT '关联的商标ID',
    source_type ENUM('WIPO', 'SWISSREG', 'OTHER') NOT NULL COMMENT '数据源类型',

    -- 分类体系信息
    classification_system ENUM('NICE', 'VIENNA', 'LOCARNO', 'OTHER') NOT NULL COMMENT '分类体系类型',
    classification_kind VARCHAR(50) DEFAULT '' COMMENT '分类种类',
    classification_version VARCHAR(20) DEFAULT '' COMMENT '分类版本',

    -- 分类代码和描述
    class_code VARCHAR(20) NOT NULL COMMENT '分类代码/号码',
    class_number VARCHAR(20) DEFAULT '' COMMENT '分类号（冗余字段，便于查询）',

    -- 多语言描述
    description_en LONGTEXT COMMENT '英文描述',
    description_zh LONGTEXT COMMENT '中文描述',
    description_fr LONGTEXT COMMENT '法文描述',
    description_de LONGTEXT COMMENT '德文描述',
    description_es LONGTEXT COMMENT '西班牙文描述',
    description_ja LONGTEXT COMMENT '日文描述',
    description_ko LONGTEXT COMMENT '韩文描述',
    description_ar LONGTEXT COMMENT '阿拉伯文描述',
    description_pt LONGTEXT COMMENT '葡萄牙文描述',
    description_it LONGTEXT COMMENT '意大利文描述',
    description_th LONGTEXT COMMENT '泰文描述',
    description_vi LONGTEXT COMMENT '越南文描述',
    description_he LONGTEXT COMMENT '希伯来文描述',
    description_uk LONGTEXT COMMENT '乌克兰文描述',
    description_ro LONGTEXT COMMENT '罗马尼亚文描述',
    description_in LONGTEXT COMMENT '印尼文描述',

    -- 分类层级（用于维也纳分类等层级结构）
    parent_class_code VARCHAR(20) DEFAULT '' COMMENT '父级分类代码',
    level_depth INT DEFAULT 1 COMMENT '分类层级深度',

    -- 状态和优先级
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否为主要分类',
    priority_order INT DEFAULT 1 COMMENT '优先级顺序',
    status ENUM('ACTIVE', 'INACTIVE', 'PENDING') DEFAULT 'ACTIVE' COMMENT '分类状态',

    -- 扩展信息
    additional_attributes JSON COMMENT '额外属性（JSON格式）',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_classification_id (classification_id),
    UNIQUE KEY uk_trademark_class_system (trademark_id, classification_system, class_code),
    INDEX idx_trademark_id (trademark_id),
    INDEX idx_classification_system (classification_system),
    INDEX idx_class_code (class_code),
    INDEX idx_class_number (class_number),
    INDEX idx_parent_class_code (parent_class_code),
    INDEX idx_create_time (create_time),

    -- 外键约束
    FOREIGN KEY fk_trademark_classifications_trademark_id (trademark_id)
        REFERENCES trademark_basic_info(trademark_id) ON DELETE CASCADE
) COMMENT='商标分类信息表';
```

## 5. 辅助表设计

### 5.1 商标图片信息表 (trademark_images)

```sql
CREATE TABLE trademark_images (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    image_id VARCHAR(100) NOT NULL COMMENT '图片ID（全局唯一）',
    trademark_id VARCHAR(100) NOT NULL COMMENT '关联的商标ID',
    source_type ENUM('WIPO', 'SWISSREG', 'OTHER') NOT NULL COMMENT '数据源类型',

    -- 图片基础信息
    image_name VARCHAR(255) DEFAULT '' COMMENT '图片名称/编号',
    image_type ENUM('LOGO', 'MARK', 'SAMPLE', 'DOCUMENT', 'OTHER') DEFAULT 'LOGO' COMMENT '图片类型',
    image_format VARCHAR(20) DEFAULT '' COMMENT '图片格式（jpg/png/svg等）',

    -- 图片URL和存储
    original_url VARCHAR(1000) DEFAULT '' COMMENT '原始图片URL',
    thumbnail_url VARCHAR(1000) DEFAULT '' COMMENT '缩略图URL',
    large_url VARCHAR(1000) DEFAULT '' COMMENT '大图URL',
    local_path VARCHAR(500) DEFAULT '' COMMENT '本地存储路径',

    -- 图片属性
    width INT DEFAULT 0 COMMENT '图片宽度',
    height INT DEFAULT 0 COMMENT '图片高度',
    file_size INT DEFAULT 0 COMMENT '文件大小（字节）',

    -- 颜色和分类信息
    colour_indicator VARCHAR(100) DEFAULT '' COMMENT '颜色标识',
    colour_claimed LONGTEXT COMMENT '声明的颜色',
    classification_kind VARCHAR(100) DEFAULT '' COMMENT '分类体系类型',
    classification_code LONGTEXT COMMENT '分类代码',
    vienna_classes VARCHAR(500) DEFAULT '' COMMENT '维也纳分类',

    -- 描述信息
    description LONGTEXT COMMENT '图片描述',
    version VARCHAR(20) DEFAULT '' COMMENT '版本号',

    -- 状态信息
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否为主要图片',
    status ENUM('ACTIVE', 'INACTIVE', 'PROCESSING', 'ERROR') DEFAULT 'ACTIVE' COMMENT '图片状态',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_image_id (image_id),
    INDEX idx_trademark_id (trademark_id),
    INDEX idx_image_type (image_type),
    INDEX idx_is_primary (is_primary),
    INDEX idx_create_time (create_time),

    -- 外键约束
    FOREIGN KEY fk_trademark_images_trademark_id (trademark_id)
        REFERENCES trademark_basic_info(trademark_id) ON DELETE CASCADE
) COMMENT='商标图片信息表';
```

### 5.2 数据源映射表 (trademark_source_mapping)

```sql
CREATE TABLE trademark_source_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    trademark_id VARCHAR(100) NOT NULL COMMENT '统一商标ID',
    source_type ENUM('WIPO', 'SWISSREG', 'OTHER') NOT NULL COMMENT '数据源类型',
    source_trademark_id VARCHAR(100) NOT NULL COMMENT '原始数据源商标ID',
    source_table_name VARCHAR(100) DEFAULT '' COMMENT '源表名称',

    -- 数据质量信息
    data_quality_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '数据质量评分（0-100）',
    completeness_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '数据完整性评分（0-100）',
    last_sync_time DATETIME COMMENT '最后同步时间',
    sync_status ENUM('SUCCESS', 'FAILED', 'PENDING', 'PARTIAL') DEFAULT 'PENDING' COMMENT '同步状态',

    -- 冲突处理
    conflict_resolution ENUM('AUTO', 'MANUAL', 'PRIORITY') DEFAULT 'AUTO' COMMENT '冲突解决方式',
    priority_level INT DEFAULT 1 COMMENT '数据源优先级（数字越小优先级越高）',

    -- 系统字段
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_source_mapping (source_type, source_trademark_id),
    INDEX idx_trademark_id (trademark_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_time (last_sync_time),

    -- 外键约束
    FOREIGN KEY fk_source_mapping_trademark_id (trademark_id)
        REFERENCES trademark_basic_info(trademark_id) ON DELETE CASCADE
) COMMENT='数据源映射表';
```

## 6. 设计说明和特性

### 6.1 核心设计原则

1. **统一标识**: 使用全局唯一的 `trademark_id` 作为所有表的关联键
2. **数据源兼容**: 通过 `source_type` 字段区分不同数据源，保持原始数据的可追溯性
3. **多语言支持**: 在分类表中提供多种语言的描述字段
4. **扩展性**: 使用 JSON 字段存储扩展数据，便于未来功能扩展
5. **数据完整性**: 通过外键约束保证数据一致性
6. **软删除**: 使用 `is_active` 字段实现软删除，保护历史数据

### 6.2 字段设计特点

- **日期字段**: 统一使用 VARCHAR(20) 存储，兼容不同格式的日期数据
- **文本字段**: 使用 LONGTEXT 存储长文本，VARCHAR 存储短文本
- **枚举字段**: 使用 ENUM 类型限制可选值，提高数据质量
- **JSON字段**: 用于存储结构化的扩展数据
- **索引优化**: 为常用查询字段建立索引，提高查询性能

### 6.3 表关系说明

```
trademark_basic_info (1)
    ├── trademark_publications (N) - 一个商标可以有多个公告
    ├── trademark_ownership (N) - 一个商标可以有多个所属关系
    ├── trademark_classifications (N) - 一个商标可以有多个分类
    ├── trademark_images (N) - 一个商标可以有多个图片
    └── trademark_source_mapping (N) - 一个商标可以来自多个数据源
```

## 7. 数据迁移建议

### 7.1 WIPO 数据源映射

| 原表 | 目标表 | 主要字段映射 |
|------|--------|-------------|
| wipo_brand_info_new | trademark_basic_info | st13→trademark_id, brand_name→brand_name |
| wipo_events_new | trademark_publications | st13→trademark_id, office_kind→publication_type |
| wipo_applicants_new | trademark_ownership | st13→trademark_id, kind→relationship_type |
| wipo_goods_services_classification_new | trademark_classifications | st13→trademark_id, class_code→class_code |
| wipo_mark_image_details_new | trademark_images | st13→trademark_id, name→image_name |

### 7.2 SwissReg 数据源映射

| 原表 | 目标表 | 主要字段映射 |
|------|--------|-------------|
| swissreg_trademark_basic_info | trademark_basic_info | trademark_id→trademark_id, publication_date→publication_date |
| swissreg_trademark_history | trademark_publications | trademark_id→trademark_id, publication_id→publication_number |
| swissreg_trademark_addresses | trademark_ownership | trademark_id→trademark_id, address_type→relationship_type |
| swissreg_trademark_vienna_classes | trademark_classifications | trademark_id→trademark_id, vienna_class_number→class_code |

## 8. 使用建议

### 8.1 查询优化

1. **基础查询**: 优先使用 `trademark_id` 进行关联查询
2. **分页查询**: 使用 `create_time` 或 `update_time` 进行排序
3. **状态过滤**: 始终添加 `is_active = 1` 条件过滤软删除数据
4. **多语言查询**: 根据用户语言偏好选择对应的描述字段

### 8.2 数据维护

1. **定期清理**: 定期清理过期的软删除数据
2. **数据同步**: 建立定时任务同步各数据源的更新
3. **质量监控**: 监控数据质量评分，及时处理数据异常
4. **版本管理**: 使用 `data_version` 字段进行数据版本控制

### 8.3 性能优化

1. **分区策略**: 可考虑按 `create_time` 进行表分区
2. **缓存策略**: 对热点商标数据进行缓存
3. **读写分离**: 查询和写入操作分离，提高并发性能
4. **批量操作**: 数据迁移时使用批量插入提高效率

---

**设计完成时间**: 2025-07-21
**版本**: v1.0
**设计者**: Augment Agent 🐾
```
```
